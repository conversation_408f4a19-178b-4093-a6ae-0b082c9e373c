package main

import (
	"log"
	"os"

	"github.com/joho/godotenv"
	"github.com/smooth-inc/backend/internal/app"
)

func main() {
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found or error loading .env file")
	}

	application := app.New()

	if err := application.Initialize(); err != nil {
		log.Fatalf("Failed to initialize application: %v", err)
	}

	defer func() {
		if err := application.Shutdown(); err != nil {
			log.Printf("Application shutdown failed: %v", err)
			os.Exit(1)
		}
	}()

	if err := application.Start(); err != nil {
		log.Fatalf("Application startup failed: %v", err)
	}
}
