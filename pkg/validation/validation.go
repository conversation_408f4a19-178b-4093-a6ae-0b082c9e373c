package validation

import (
	"regexp"
	"strings"
	"unicode"

	"github.com/smooth-inc/backend/pkg/errors"
)

var (
	emailRegex    = regexp.MustCompile(`^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,}$`)
	usernameRegex = regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)
	phoneRegex    = regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
	plateRegex    = regexp.MustCompile(`^[A-Z0-9\p{Hiragana}ひらがな\p{Han}]+$`)
)

type Validator struct {
	errors []string
}

func New() *Validator {
	return &Validator{
		errors: make([]string, 0),
	}
}

func (v *Validator) AddError(message string) {
	v.errors = append(v.errors, message)
}

func (v *Validator) HasErrors() bool {
	return len(v.errors) > 0
}

func (v *Validator) GetErrors() []string {
	return v.errors
}

func (v *Validator) GetFirstError() string {
	if len(v.errors) > 0 {
		return v.errors[0]
	}
	return ""
}

func (v *Validator) ToError() error {
	if v.HasErrors() {
		return errors.NewValidationError(strings.Join(v.errors, "; "))
	}
	return nil
}

func (v *Validator) Required(value string, fieldName string) *Validator {
	if strings.TrimSpace(value) == "" {
		v.AddError(fieldName + " is required")
	}
	return v
}

func (v *Validator) MinLength(value string, min int, fieldName string) *Validator {
	if len(value) < min {
		v.AddError(fieldName + " must be at least " + string(rune(min)) + " characters long")
	}
	return v
}

func (v *Validator) MaxLength(value string, max int, fieldName string) *Validator {
	if len(value) > max {
		v.AddError(fieldName + " must be at most " + string(rune(max)) + " characters long")
	}
	return v
}

func (v *Validator) Email(email string, fieldName string) *Validator {
	if email != "" && !emailRegex.MatchString(email) {
		v.AddError(fieldName + " must be a valid email address")
	}
	return v
}

func (v *Validator) Username(username string, fieldName string) *Validator {
	if username != "" && !usernameRegex.MatchString(username) {
		v.AddError(fieldName + " can only contain letters, numbers, underscores, and hyphens")
	}
	return v
}

func (v *Validator) Phone(phone string, fieldName string) *Validator {
	if phone != "" && !phoneRegex.MatchString(phone) {
		v.AddError(fieldName + " must be a valid phone number")
	}
	return v
}

func (v *Validator) Password(password string, fieldName string) *Validator {
	if password == "" {
		v.AddError(fieldName + " is required")
		return v
	}

	if len(password) < 8 {
		v.AddError(fieldName + " must be at least 8 characters long")
	}

	var hasUpper, hasLower, hasDigit bool
	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsDigit(char):
			hasDigit = true
		}
	}

	if !hasUpper {
		v.AddError(fieldName + " must contain at least one uppercase letter")
	}
	if !hasLower {
		v.AddError(fieldName + " must contain at least one lowercase letter")
	}
	if !hasDigit {
		v.AddError(fieldName + " must contain at least one digit")
	}

	return v
}

func (v *Validator) PlateNumber(plateNumber string, fieldName string) *Validator {
	if plateNumber != "" && !plateRegex.MatchString(plateNumber) {
		v.AddError(fieldName + " must be a valid Japanese plate number format")
	}
	return v
}

func (v *Validator) OneOf(value string, options []string, fieldName string) *Validator {
	if value == "" {
		return v
	}

	for _, option := range options {
		if value == option {
			return v
		}
	}

	v.AddError(fieldName + " must be one of: " + strings.Join(options, ", "))
	return v
}

func IsValidEmail(email string) bool {
	return emailRegex.MatchString(email)
}

func IsValidUsername(username string) bool {
	return usernameRegex.MatchString(username)
}

func IsValidPhone(phone string) bool {
	return phoneRegex.MatchString(phone)
}

func IsValidPlateNumber(plateNumber string) bool {
	return plateRegex.MatchString(plateNumber)
}

func ValidateEmail(email string) error {
	if !IsValidEmail(email) {
		return errors.NewValidationError("invalid email format")
	}
	return nil
}

func ValidateUsername(username string) error {
	if len(username) < 3 {
		return errors.NewValidationError("username must be at least 3 characters long")
	}
	if len(username) > 50 {
		return errors.NewValidationError("username must be at most 50 characters long")
	}
	if !IsValidUsername(username) {
		return errors.NewValidationError("username can only contain letters, numbers, underscores, and hyphens")
	}
	return nil
}

func ValidatePassword(password string) error {
	validator := New()
	validator.Password(password, "password")
	return validator.ToError()
}
