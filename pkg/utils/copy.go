package utils

import (
	"bytes"
	"encoding/gob"
	"encoding/json"
	"fmt"
	"reflect"
	"time"
)

// DeepCopy creates a deep copy of the given value using gob encoding
// This method works for most Go types but requires types to be registered for interfaces
func DeepCopy(src, dst interface{}) error {
	var buf bytes.Buffer
	if err := gob.NewEncoder(&buf).Encode(src); err != nil {
		return fmt.Errorf("failed to encode: %w", err)
	}
	return gob.NewDecoder(&buf).Decode(dst)
}

// DeepCopyJSON creates a deep copy using JSON marshaling/unmarshaling
// This is more reliable for complex types but may lose type information
func DeepCopyJSON(src, dst interface{}) error {
	data, err := json.Marshal(src)
	if err != nil {
		return fmt.Errorf("failed to marshal: %w", err)
	}
	return json.Unmarshal(data, dst)
}

// DeepCopyReflection creates a deep copy using reflection
// This is the most robust method but also the slowest
func DeepCopyReflection(src interface{}) interface{} {
	return deepCopyReflectionValue(reflect.ValueOf(src)).Interface()
}

func deepCopyReflectionValue(src reflect.Value) reflect.Value {
	switch src.Kind() {
	case reflect.Ptr:
		if src.IsNil() {
			return src
		}
		dst := reflect.New(src.Type().Elem())
		dst.Elem().Set(deepCopyReflectionValue(src.Elem()))
		return dst

	case reflect.Slice:
		if src.IsNil() {
			return src
		}
		dst := reflect.MakeSlice(src.Type(), src.Len(), src.Cap())
		for i := 0; i < src.Len(); i++ {
			dst.Index(i).Set(deepCopyReflectionValue(src.Index(i)))
		}
		return dst

	case reflect.Map:
		if src.IsNil() {
			return src
		}
		dst := reflect.MakeMap(src.Type())
		for _, key := range src.MapKeys() {
			dst.SetMapIndex(key, deepCopyReflectionValue(src.MapIndex(key)))
		}
		return dst

	case reflect.Struct:
		dst := reflect.New(src.Type()).Elem()
		for i := 0; i < src.NumField(); i++ {
			if dst.Field(i).CanSet() {
				dst.Field(i).Set(deepCopyReflectionValue(src.Field(i)))
			}
		}
		return dst

	case reflect.Array:
		dst := reflect.New(src.Type()).Elem()
		for i := 0; i < src.Len(); i++ {
			dst.Index(i).Set(deepCopyReflectionValue(src.Index(i)))
		}
		return dst

	case reflect.Interface:
		if src.IsNil() {
			return src
		}
		return deepCopyReflectionValue(src.Elem())

	default:
		return src
	}
}

// ShallowCopy creates a shallow copy of a struct
func ShallowCopy(src, dst interface{}) error {
	srcVal := reflect.ValueOf(src)
	dstVal := reflect.ValueOf(dst)

	if srcVal.Kind() == reflect.Ptr {
		srcVal = srcVal.Elem()
	}
	if dstVal.Kind() != reflect.Ptr || dstVal.Elem().Kind() != reflect.Struct {
		return fmt.Errorf("destination must be a pointer to struct")
	}
	dstVal = dstVal.Elem()

	if srcVal.Kind() != reflect.Struct {
		return fmt.Errorf("source must be a struct")
	}

	if srcVal.Type() != dstVal.Type() {
		return fmt.Errorf("source and destination must be the same type")
	}

	for i := 0; i < srcVal.NumField(); i++ {
		if dstVal.Field(i).CanSet() {
			dstVal.Field(i).Set(srcVal.Field(i))
		}
	}

	return nil
}

// CopySlice creates a copy of a slice
func CopySlice[T any](src []T) []T {
	if src == nil {
		return nil
	}
	dst := make([]T, len(src))
	copy(dst, src)
	return dst
}

// CopyMap creates a copy of a map (shallow copy of values)
func CopyMap[K comparable, V any](src map[K]V) map[K]V {
	if src == nil {
		return nil
	}
	dst := make(map[K]V, len(src))
	for k, v := range src {
		dst[k] = v
	}
	return dst
}

// CopyStruct creates a copy of a struct by copying all exported fields
func CopyStruct[T any](src T) T {
	var dst T
	srcVal := reflect.ValueOf(src)
	dstVal := reflect.ValueOf(&dst).Elem()

	if srcVal.Kind() == reflect.Ptr {
		if srcVal.IsNil() {
			return dst
		}
		srcVal = srcVal.Elem()
	}

	if srcVal.Kind() != reflect.Struct || dstVal.Kind() != reflect.Struct {
		return src // Return original if not struct
	}

	for i := 0; i < srcVal.NumField(); i++ {
		srcField := srcVal.Field(i)
		dstField := dstVal.Field(i)

		if dstField.CanSet() && srcField.IsValid() {
			dstField.Set(srcField)
		}
	}

	return dst
}

// MustDeepCopy performs a deep copy and panics on error
func MustDeepCopy[T any](src T) T {
	var dst T
	if err := DeepCopyJSON(src, &dst); err != nil {
		panic(fmt.Sprintf("deep copy failed: %v", err))
	}
	return dst
}

// CloneWithModifications creates a copy of a struct and applies modifications
func CloneWithModifications[T any](src T, modifyFunc func(*T)) T {
	dst := CopyStruct(src)
	if modifyFunc != nil {
		modifyFunc(&dst)
	}
	return dst
}

// IsDeepEqual checks if two values are deeply equal
func IsDeepEqual(a, b interface{}) bool {
	return reflect.DeepEqual(a, b)
}

// CopyTime safely copies a time.Time value
func CopyTime(src *time.Time) *time.Time {
	if src == nil {
		return nil
	}
	dst := *src
	return &dst
}

// CopyTimeValue copies a time.Time value
func CopyTimeValue(src time.Time) time.Time {
	return src
}
