package utils

import (
	"reflect"
	"strings"
)

// GetStructFieldNames returns all field names of a struct
func GetStructFieldNames(v interface{}) []string {
	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return nil
	}

	var fields []string
	for i := 0; i < val.NumField(); i++ {
		field := val.Type().Field(i)
		if field.IsExported() {
			fields = append(fields, field.Name)
		}
	}

	return fields
}

// GetStructFieldTags returns all tag values for a given tag key
func GetStructFieldTags(v interface{}, tagKey string) map[string]string {
	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return nil
	}

	tags := make(map[string]string)
	for i := 0; i < val.NumField(); i++ {
		field := val.Type().Field(i)
		if field.IsExported() {
			if tag := field.Tag.Get(tagKey); tag != "" {
				tags[field.Name] = tag
			}
		}
	}

	return tags
}

// HasField checks if a struct has a specific field
func HasField(v interface{}, fieldName string) bool {
	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return false
	}

	_, found := val.Type().FieldByName(fieldName)
	return found
}

// SetFieldValue sets a field value by name using reflection
func SetFieldValue(v interface{}, fieldName string, value interface{}) bool {
	val := reflect.ValueOf(v)
	if val.Kind() != reflect.Ptr || val.Elem().Kind() != reflect.Struct {
		return false
	}

	val = val.Elem()
	field := val.FieldByName(fieldName)
	if !field.IsValid() || !field.CanSet() {
		return false
	}

	fieldValue := reflect.ValueOf(value)
	if field.Type() != fieldValue.Type() {
		return false
	}

	field.Set(fieldValue)
	return true
}

// GetFieldValue gets a field value by name using reflection
func GetFieldValue(v interface{}, fieldName string) (interface{}, bool) {
	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return nil, false
	}

	field := val.FieldByName(fieldName)
	if !field.IsValid() {
		return nil, false
	}

	return field.Interface(), true
}

// CopyStructFields copies fields from source to destination struct
// Only copies fields that exist in both structs and have the same type
func CopyStructFields(dst, src interface{}) {
	dstVal := reflect.ValueOf(dst)
	srcVal := reflect.ValueOf(src)

	if dstVal.Kind() != reflect.Ptr || dstVal.Elem().Kind() != reflect.Struct {
		return
	}
	if srcVal.Kind() == reflect.Ptr {
		srcVal = srcVal.Elem()
	}
	if srcVal.Kind() != reflect.Struct {
		return
	}

	dstVal = dstVal.Elem()

	for i := 0; i < srcVal.NumField(); i++ {
		srcField := srcVal.Field(i)
		srcType := srcVal.Type().Field(i)

		if !srcType.IsExported() {
			continue
		}

		dstField := dstVal.FieldByName(srcType.Name)
		if !dstField.IsValid() || !dstField.CanSet() {
			continue
		}

		if dstField.Type() == srcField.Type() {
			dstField.Set(srcField)
		}
	}
}

// StructToMap converts a struct to a map[string]interface{}
func StructToMap(v interface{}) map[string]interface{} {
	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return nil
	}

	result := make(map[string]interface{})
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := val.Type().Field(i)

		if !fieldType.IsExported() {
			continue
		}

		// Use json tag if available, otherwise use field name
		name := fieldType.Name
		if jsonTag := fieldType.Tag.Get("json"); jsonTag != "" {
			parts := strings.Split(jsonTag, ",")
			if parts[0] != "" && parts[0] != "-" {
				name = parts[0]
			}
		}

		result[name] = field.Interface()
	}

	return result
}
