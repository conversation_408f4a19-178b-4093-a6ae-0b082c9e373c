openapi: 3.0.3
info:
  title: Smooth Parking Backend API
  description: |
    A comprehensive parking management system backend with advanced security middleware.
    
    ## Security Features
    - **API Key Authentication**: HMAC-SHA256 signed requests with timestamp validation
    - **JWT Authentication**: Bearer token authentication for user sessions
    - **Rate Limiting**: Token bucket algorithm with configurable limits
    - **Request Validation**: Input sanitization and security pattern detection
    - **Comprehensive Logging**: Structured request/response logging with correlation IDs
    
    ## Response Format
    All API responses follow a standardized format with consistent headers and structure.
    Error responses include detailed validation information and trace IDs for debugging.
    
    ## Rate Limiting
    - Default: 100 requests per minute per client
    - Headers: `X-Rate-Limit-*` indicate current usage and limits
    - 429 status code returned when limits exceeded
    
    ## Request Requirements
    - Content-Type: application/json (for POST/PUT requests)
    - Request size limit: 1MB
    - Timestamp tolerance: 300 seconds for signed requests
  version: "1.0.0"
  termsOfService: https://example.com/terms
  contact:
    name: Smooth Parking API Support
    email: <EMAIL>
    url: https://docs.smooth-parking.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api/v1
    description: Development server
  - url: https://api-staging.smooth-parking.com/v1
    description: Staging server
  - url: https://api.smooth-parking.com/v1
    description: Production server

security:
  - bearerAuth: []
  - apiKeyAuth: []

# Note: For path and component references, you would need to use a tool that supports
# multi-file OpenAPI specs such as Redocly or Swagger CLI that can bundle them at build time.
# Below shows the path structure, but you would need to manually combine them or use a tool.

paths:
  # Authentication endpoints
  /auth/register:
    $ref: './paths/auth.yaml#/~1auth~1register'
  /auth/login:
    $ref: './paths/auth.yaml#/~1auth~1login'
  /auth/refresh:
    $ref: './paths/auth.yaml#/~1auth~1refresh'
  # Add more path references as needed

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT token authentication for user sessions.
        Include the token in the Authorization header: `Authorization: Bearer <token>`
    
    apiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: |
        API key authentication with HMAC signature validation.
        Required headers:
        - `X-API-Key`: Your API key
        - `X-Signature`: HMAC-SHA256 signature of the request
        - `X-Timestamp`: Unix timestamp (must be within 300 seconds)
        
        Signature calculation:
        ```
        data = METHOD + PATH + QUERY + BODY + TIMESTAMP
        signature = HMAC-SHA256(data, api_secret)
        ```

  parameters:
    RequestId:
      name: X-Request-ID
      in: header
      description: Optional request identifier for tracking
      schema:
        type: string
        format: uuid

    Timestamp:
      name: X-Timestamp
      in: header
      required: true
      description: Unix timestamp for request validation (required for API key auth)
      schema:
        type: integer
        format: int64

    Signature:
      name: X-Signature
      in: header
      required: true
      description: HMAC-SHA256 signature for request authentication (required for API key auth)
      schema:
        type: string

  responses:
    StandardSuccess:
      description: Successful operation
      headers:
        X-Request-ID:
          description: Request identifier for tracking
          schema:
            type: string
        X-Response-Time:
          description: Processing time in milliseconds
          schema:
            type: string
        X-Rate-Limit-Remaining:
          description: Requests remaining in current window
          schema:
            type: integer
        X-Rate-Limit-Reset:
          description: Unix timestamp when rate limit resets
          schema:
            type: integer
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/StandardResponse'

    ValidationError:
      description: Request validation failed
      headers:
        X-Request-ID:
          description: Request identifier for tracking
          schema:
            type: string
        X-Response-Time:
          description: Processing time in milliseconds
          schema:
            type: string
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            validation_error:
              summary: Validation Error Example
              value:
                error: "VALIDATION_ERROR"
                message: "Request validation failed"
                details:
                  - field: "email"
                    code: "INVALID_FORMAT"
                    message: "Email format is invalid"
                metadata:
                  timestamp: "2023-12-01T12:00:00Z"
                  request_id: "550e8400-e29b-41d4-a716-446655440000"
                  version: "v1"
                  processing_time: "15ms"

    RateLimitExceeded:
      description: Rate limit exceeded
      headers:
        X-Rate-Limit-Remaining:
          description: Requests remaining (0 when rate limited)
          schema:
            type: integer
        X-Rate-Limit-Reset:
          description: Unix timestamp when rate limit resets
          schema:
            type: integer
        Retry-After:
          description: Seconds to wait before retrying
          schema:
            type: integer
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "RATE_LIMIT_EXCEEDED"
            message: "Too many requests. Please try again later."
            metadata:
              timestamp: "2023-12-01T12:00:00Z"
              request_id: "550e8400-e29b-41d4-a716-446655440000"

    Unauthorized:
      description: Authentication required or invalid
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "UNAUTHORIZED"
            message: "Authentication required"

    Forbidden:
      description: Access denied
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "FORBIDDEN"
            message: "Access denied"

    InternalServerError:
      description: Internal server error
      headers:
        X-Request-ID:
          description: Request identifier for tracking
          schema:
            type: string
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "INTERNAL_ERROR"
            message: "An internal error occurred"
            trace_id: "abc123def456"

# Include all the path definitions
paths:
  $ref: "./paths/auth.yaml"

# Include component definitions
components:
  schemas:
    $ref: "./components/schemas.yaml"
  responses:
    $ref: "./components/responses.yaml" 