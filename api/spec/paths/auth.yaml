/auth/register:
  post:
    tags: [Authentication]
    summary: Register a new user
    security: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../master.yaml#/components/schemas/RegisterRequest'
    responses:
      '201':
        description: User registered successfully
        content:
          application/json:
            schema:
              $ref: '../parking-api.yaml#/components/schemas/AuthResponse'
      '400':
        $ref: '../master.yaml#/components/responses/BadRequest'
      '409':
        $ref: '../master.yaml#/components/responses/Conflict'

/auth/login:
  post:
    tags: [Authentication]
    summary: Login user
    security: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../master.yaml#/components/schemas/LoginRequest' 
    responses:
      '200':
        description: Login successful
        content:
          application/json:
            schema:
              $ref: '../parking-api.yaml#/components/schemas/AuthResponse' 
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'

/auth/refresh:
  post:
    tags: [Authentication]
    summary: Refresh access token
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../parking-api.yaml#/components/schemas/RefreshTokenRequest' 
    responses:
      '200':
        description: Token refreshed successfully
        content:
          application/json:
            schema:
              $ref: '../parking-api.yaml#/components/schemas/AuthResponse' 
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized' 