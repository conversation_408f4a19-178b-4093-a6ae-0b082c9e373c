BadRequest:
  description: Bad request or validation error
  content:
    application/json:
      schema:
        $ref: '../master.yaml#/components/schemas/Error' 

Unauthorized:
  description: Unauthorized access
  content:
    application/json:
      schema:
        $ref: '../master.yaml#/components/schemas/Error' 

Forbidden:
  description: Forbidden access
  content:
    application/json:
      schema:
        $ref: '../master.yaml#/components/schemas/Error' 

NotFound:
  description: Resource not found
  content:
    application/json:
      schema:
        $ref: '../master.yaml#/components/schemas/Error' 

Conflict:
  description: Resource conflict
  content:
    application/json:
      schema:
        $ref: '../master.yaml#/components/schemas/Error' 

# Other response definitions from the original file 