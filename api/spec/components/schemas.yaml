RegisterRequest:
  type: object
  required:
    - email
    - password
    - name
  properties:
    email:
      type: string
      format: email
      example: <EMAIL>
    password:
      type: string
      format: password
      minLength: 8
      example: Password123!
    name:
      type: string
      example: <PERSON>
    phone:
      type: string
      example: '+1234567890'

LoginRequest:
  type: object
  required:
    - email
    - password
  properties:
    email:
      type: string
      format: email
      example: <EMAIL>
    password:
      type: string
      format: password
      example: Password123!

# Other schemas from the original file
# This is just an example of how to split schemas into their own file 

components:
  schemas:
    # Common Response Headers added by middleware
    ResponseHeaders:
      type: object
      properties:
        X-Request-ID:
          type: string
          description: Unique request identifier for tracking
          example: "550e8400-e29b-41d4-a716-446655440000"
        X-Response-Time:
          type: string
          description: Time taken to process the request
          example: "45ms"
        X-Rate-Limit-Remaining:
          type: integer
          description: Number of requests remaining in the current window
          example: 95
        X-Rate-Limit-Reset:
          type: integer
          description: Unix timestamp when the rate limit resets
          example: 1640995200
        X-Rate-Limit-Limit:
          type: integer
          description: Total number of requests allowed per window
          example: 100
        X-Content-Type-Options:
          type: string
          description: Content type options for security
          example: "nosniff"
        X-Frame-Options:
          type: string
          description: Frame options for security
          example: "DENY"
        X-XSS-Protection:
          type: string
          description: XSS protection header
          example: "1; mode=block"

    # Standardized Success Response
    StandardResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Indicates if the request was successful
          example: true
        message:
          type: string
          description: Human-readable message about the operation
          example: "Operation completed successfully"
        data:
          type: object
          description: Response data (varies by endpoint)
        metadata:
          $ref: '#/components/schemas/ResponseMetadata'
        errors:
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'

    # Response Metadata
    ResponseMetadata:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
          description: ISO 8601 timestamp of the response
          example: "2023-12-01T12:00:00Z"
        request_id:
          type: string
          description: Unique request identifier
          example: "550e8400-e29b-41d4-a716-446655440000"
        version:
          type: string
          description: API version
          example: "v1"
        processing_time:
          type: string
          description: Time taken to process the request
          example: "45ms"

    # Validation Error
    ValidationError:
      type: object
      properties:
        field:
          type: string
          description: Field that failed validation
          example: "email"
        code:
          type: string
          description: Error code
          example: "INVALID_FORMAT"
        message:
          type: string
          description: Human-readable error message
          example: "Email format is invalid"

    # Standard Error Response (Enhanced)
    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Machine-readable error code
          example: "VALIDATION_ERROR"
        message:
          type: string
          description: Human-readable error message
          example: "The request contains invalid data"
        details:
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
          description: Detailed validation errors
        metadata:
          $ref: '#/components/schemas/ResponseMetadata'
        trace_id:
          type: string
          description: Distributed tracing identifier
          example: "abc123def456" 