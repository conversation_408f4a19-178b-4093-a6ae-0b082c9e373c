// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
)

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+x9aW8cyXnwXyn0u8BLITOaoUQba35JKEqy6KVkgpR2s5Fpotj9zEwt+9qqalJjmUCG",
	"MAwnjmEkRj4EgREECIIgJxAgyIcAyY8Z+H8EdfVZfXEOyet8I6frfK566rnqveNGQRyFEHLm7L93KLA4",
	"ChnIf55g7xS+ToBx8Z8bhRxC+SeOY5+4mJMoHH3FolD8xtwZBFj89QmFibPv/L9RNvRIfWWjZ5RG9FRP",
	"4tze3g4cD5hLSSwGc/bFnMhMejtwDqNw4hN3iwtIZ7wdOM8jekk8D8LtTZ9NeTtwjkIONMT+GdBroLLv",
	"9lZiJkdqdqSmvx04ryL+PEpCb3tLeRVxpKa8HThvQpzwWUTJj2CLSyjMKj7rnmLgA5eTa8zhhBKXhFNB",
	"QWSa45yYRjFQThRXwWQCogM8p1EgfphENMDc2Xc8zGHISQDOwOHzGJx9h3FKwqnYdtrtTciJ37Xf7cCh",
	"8HVCqIDV29Lc52nz6PIrUCR/kPBZCofK2rHrAmOvoyvFEtVFvosJBXaU/0pCDlOQlBPjeQAhPwOexKfp",
	"st6XQP3FDPgMKNKtERPNEWEo3Um67sso8gFLXqEwocBm9WuTwxyltNJEDWdZ04wmBk7CgLZ1fSPaCLDb",
	"IBudqC3V0gYOokQtrySUsO8mPubgoRjTKxJO0QQAkRB97+TLDBw5ULtpj+cAbYt+DvCEAr7yohsJyymE",
	"QDGHU3CBxHo9E5z43NnnNIEyc3xXt0c49BCD0ENU9UQ44VGAOXGx78+teBPNX0WcTDTjtk52JsY3tCE2",
	"44P4gsL8ILaZEgZP1cAaDS+BzyKvdcI3DJBA/f9nSLdLpw/UCNXZSnyn8VpGy3kbmdTyYUonNsxT7wnF",
	"Sj5XuEB8PcaM79m/JpRC6M4LMHEKNJY1JuF1RFx4lQSXii8qLQJgDE/l+uEdFphy9h29ORTTSAgT8BBL",
	"pFiZJAUayYaJMfEOeHdRqbFz5BW6JAnxGlorYngtv1q2ogn6DfXtwoVjnrA2PtNbP1ONZTdKYtA/K4Fz",
	"ZMebhlHuW57WKlT0JIqElKhSjotDF3xfcomWDDYSko2gF9RdCoKu+3SB0HstPnXuMIsS6s9PMa9ZOOmK",
	"cilDjyPejrG0ZaFfV9ry9VIb55CNTOuOIzOOKe8HvG40qikno1EecewfNAicJPb6Il6I004bbaDts3Q7",
	"ECaBkLKuULxoIBWElITF3+qQkH+H0QWbRTc50Zut6nCGwymcYMZuIurVHtJKSHLTznJaqwYoNi0sc4Vw",
	"Uz/AK7hJO6OdgIQkSAL0KXJnmGKXA2UPnIETkPAYwimfOfuftil+5TUXF2A7hw4lO2tY1yuzfTn4fiy0",
	"KaYoQcnMVVpmfuBMaNUDTTJ0Pfn4mLGCuhPgdwaRj21Cj1A8xSEuNd2tA5Y5w1pljmwoQTCtrmR3bAMw",
	"UIL97MTPtd9rA66eZVAGQG6DpQkaINzppiXlwfQVVvRQ2Jttc7Ea9DTxof0kz7etMFs2b2lU245sOmm9",
	"5mdVqZ5a1VKkBXMH/So39Xr0j6fAxUUzCp8Sqv7IC2oIORUrgXeEWyVx2r3pKmod9UKrIXr0CwpuRD35",
	"PwPGSBReaLCow0APY1tE5QrV406GdiYRRWIFSM0gJXb17Myhs05UPk1o7mKUX8FrcTKnk3u6nVhBQMKE",
	"A+u6Ck0AbURvu5oYoZPJBPvV0EOC/JBsi0LV2CpgJI66yvt+RPkswMT/HGgqe2olBzdWhOJm5ADoOjcC",
	"ki3RhEYBCvCUuMgn4VXrQaPGt0mDoiHKItI8O7F4wDHx86DIxgRjOGy6oFW+iROTcRzEPQ7TytQFy0Jl",
	"N5eYQe0lxMPEnx/i+CCOfQJeTSPCXKGZNumnExJiv3aakExnvG2a6BooJV7dYmt2biQEicITCtcEbmoJ",
	"7v5nz8BhLoSYkkh2JBwC1vFWc6Y7Otn6MaV4XtWL8jPmJzzvvvE6iu6/erPsU2CJz62Lry4qotOIt6r2",
	"IBi8QO/ql1a7qmx13mHiXue66YYoMOCIhIzTRAlyxMRBzyM0jxKK6ldZWdALTL0bTCF3xNYA4/FxVQI+",
	"HnpkSjgq6nCZQI8x50BFyx++HQ+/c/7+8e0nNsl9uGs5UAmfo2vsJ4B2vodjHAIDpLRG5JMrQL/51eI3",
	"//lrefVp0VNfWIbfRT6IxaEXWt1EEUWYBvMhBXWM6+87zwYvBp8NXg5eD74sT1bc4XLxl8vFXy0Xf71c",
	"/Hq5+Jvl4m+Xi79bLv5+ufiH5eIfl4t/Wi7+ebn4l+XiX5eLf1su/n25+I/l4r+Wi/9eLv5neffHy7vF",
	"8u5uefeT5d1Pl3d/srz70+Xdz5d3v1gu7paLnywXP10ufrZc/Hy5+MVy8cvl4s+Xi18t7/5ieffL5d2f",
	"Le9+9uzFZy9ff3luhe/R0yoA9jTulI7dgLM9O86kWutB6FqUoZSaUNYKMTei4pzIDo8oufRBgVRcaSVA",
	"9fXW2c8QqZd2O3BIgKegjW7FGb8v/8A+kk3Qm9NjdQp7ZiUNt09iuXN/W0PHqFN+xJHYBycTYgfTt+1g",
	"yswsxQnG+0heI8ETbCumQTtSZX0wQLv7yIcJR3wGhQXsCN3tgbx0Sh13PNg9tylw3DLfa3N+o2hSgMo9",
	"LsZciP3DXWcghIJkL0lieYCmG7dJweNoSuoFTQ7MlV28YUBDHIBgVinlEPY8KpQ+K3rrLCpimAZ7TGm7",
	"Jbw3WEmOI161Q2HpYpNkLnAUYsEy4t4bMfCsV42XSvp3Oh6KGzNdkGnRrCaXHB1dtOZXWkGy2ZLljxYG",
	"zpEmKEdEdqiNP90fj4v8tPN2PNw9/33JVj9+9HY8fHz+YP/tePitc/nTJ7W2nuLIjx6tYeQSKahp1DYG",
	"csc2Mig7sEpw6m8Z72jIJuwMCmpvzt3V4WrZwwrXy6UidJM+u+136+OE+zVXlg6WrzyqjAFs2xbsyhpy",
	"wkOgBpQJUoL8Im+9FvcQxvH84gbTUAyv7EtwgX2QZHqp7LUXFAISegUbWrb2k4L7o2RW0dLV6h0UAsyY",
	"V1dkXzcKOXb5ySwK7bi8B9NoI/fBNSY+viQ+4fPu/p1CL3P9fInfGWdTUey+VAoMkq2Q9H03OOM9wji2",
	"6036izTaAAeqbTYMMHVnQu9PfC6tNlUlqqIsTQDzhELxJlXDJOayNHAmFOClshdV1/ecQqaRaKuSdYsz",
	"EMfEMQkIPwzsl+ii8650jLVAsKs8FLpgz/0T9mhvZheh4grLE6/kTqiFvx+F0z7tQ22ZriwxiiFcE58J",
	"3sY8oq/q5urmFcz0HOMRPIsjztbkELTJyBrOrAos9dWHhgX5mPE32urbWZpErpvEOHRTiu2A0WbING9T",
	"OTPavBhrkZOqy5N5J57aarxaD83nQGnZVsbtr9+sYH9bM7mz+luAL8RrDYlrmutj/9NRDWWBGE0mDGqm",
	"kQTek7ZTM6PFe8zpvJ//WFyI+/WokbIlPT/E2rlrVpSb6ty6t9Rl0i2ET1mom0P2Vgjcuq+21DHWa4KJ",
	"n1A4BazDau/Nt61BY/eM9lotfosCp/PDeh+CvqG0B46qZv09WRuPH8u3PCbhVWO7zKZRvfhs+a6kl9zi",
	"F6t3cckvh9ptZi5ZJGTJZEJcIi5YkyT0mKM47MID1yeh9h/HQjpciN+Vs9hcydKwTx3DKG5cagXnVt7x",
	"/UvsXimLpUXJPjAqDDJNUaTaKrt3gMNEOnqVd197GLtruE2WAEn4Ym77QWozHU2wzzrajnL0dr9gVh1Q",
	"fp/ozyZuiLN12SVCw2ayqOGSM9OI7nKYAPWQ/IZ2PicMD9BLzDhQQVUDBNx9+GBd0ZQCVvOXUchnNcuQ",
	"LZTHJhDN0M7ucPeRPSZAjfYlYNo+2Fy0qr+2WZS3p4aY6oL/+YwwRJi0iHeOulaa/l51VHFwoj0kLfwM",
	"RRMkoN8UhpCXlo1xMcbkVJzvpBiIIxsNsjhFJVBiPI/x3CoyPpCQlXkPn2OfeLgl+CaLBHkWCunRkMYh",
	"2g4N6ghDoDqgSUQldmVWhQ2XM8zq4/XtNKNGQzPMEK4hG8TAB5fXZJHMMLvXZOG8NA1D2PNqJiFMAvpQ",
	"G/Y6b+haYKa8HQHGPIiZdcoQ3vEzDrEt1vQdRxTcKAgg9MBDjEOMeGRSK0Dl3+TIF3te9SRkwC80wKsf",
	"FcovxDLNR8cQ+UXlmA3ByhNaW5f3Pls0VAVoIYDHxE44vgIdACX+VaspAM3pdZxVPT4xhJ4xxxqNoBR4",
	"LDRo+QcFoXDUOIJOTMB4W8xoSSDb3PFoZ/fR45ID+/F6jpp8SGpxJalzPY1VRjvLxV3Vj76hi39TLNrz",
	"xPfRNA1Ia4tFWzWYtjh5OaJhp3s0QznqtpwPlXPnS5zvlQbd+xhOmDwwDduEYhxfMUoA1CXyHwohl38E",
	"xCccU/sReUKJC9/XcVnWwPSVTQTGuRIDvUhCZXlpdnamTsmOEA0Jvwgy23s6+O7AZmCx2CuK7snCeJXV",
	"W80YmS2rCkMPz4s2JYO3lzKO4HUiVvCFFGqvZ4kzcJ5T4gycMyxWdJbYo2rLlxONqB5m7h//8NHe/nj8",
	"SZ0d714IW9HMvgImJZTXiklmQyXx5xedHPYkJJxg/2JCAaxbsvbyI35Ro+3LCEsxd3cDZRpzYDNPap7v",
	"Ye4siArLkNQArfNwhmfaIic1WGqgOsjhxazChtvTXG5zbRSNB9fE1ZkyNaFSqkkurAkpX6O0VCFOsXul",
	"FBeL8tWYXV1J9ci1tm9oSsQlePVAzEx4W856LSRzR+Eje+ZHbdxQGoEZJIyjS0CYIx/EbTKfjdWWjDVw",
	"YuPnLg0vfs5UkMJCrdINJkApeMc4nCZpPJCx3X6FnXLu8onpgXzdBbmRB2jnKyxxb7SSAYJQ/vAsnPqE",
	"zXIxZ2pYsEtzeTfol06d6umRzuwvXWi8RAwu1STGaWM6N7Xj/k1Ivk7UJUCGjol9+dGUhEUQf2tcQNvj",
	"YogfHv7oYPhH4+F3Lobnv9ceJ5SuZpBSai7cLDS5QGX82ZmDQeh1ylNI2cSWp6BDOQSgqRyynLmQjypY",
	"Nc5ZLLo9vjrulPLYzk41+RlyETolw8RH68z9bvtT47bE/JW2ukpEd5aZZRe6nsye0ibKbJSRoeYOincp",
	"Wr5qylF5D6eVI7BVc7skvrRa54JHqnrBZT77o08NikldwkYuXuUNq0vYqNXopQOzYcVWEGbOn7X5+vrf",
	"udMQ3sNCHHaHqIQuSTIme6wRmdI7epQLybZk9Pf36FIa0ZcNLgp4R3jzpL19wp1tDSe4oNTmwyulUyZ/",
	"AW0KQ9h0nYFuCXzGab3pygQdXJmapzJX5rYNEnoBDal2eRatqDChtDMa1fkGM5Qlnnbl5zp9/WlZTa/v",
	"/sqqAL1IAhwOKWBPWjq12q+VkJqRXlsdGXopog/aCaJL4sMAecCueBQPEBfD8wercFh8kAWcluqPnaQa",
	"TDTJQ9vOqbroQjePko4UrY5YciWJA8ZGANKfJBCrRs2TQcI600ADWTYkFeSNyvkqF/UOaD1mQ2CR3kCP",
	"HLwS93RKwrOV1bKYuon0RLkULIA/k445UxJMDoVUD8RUF2ukC+NRYGSFdUDTBMmsluoJE88gAIr9z2Bu",
	"UbfNV3QFc3nbUJyCzp5+Zs9TSS7FJUtwj3VAvahcMzFyBxWxdA86PZarMY3QxI9uEJlIzwjURO6nKGqA",
	"****************************************+Igt5Q7s4C/eS+2UsT6AFbJOCqNWl2y7lqgA14+v",
	"MkbNQt+wBvNPF/+y+jDyCEvdebLSXIq3giuuSgaexbfchxpSz2thViu/d7qm79wQ3zc1DhGFYeG2TibI",
	"leWJvAcdru1rsYrJgnrzZzVrL0JfpeblS/DV+KHlmCcJm7UOGSds1mHEjVrWtmlK+/D2rCqn6oKX3Ziz",
	"web3LGzlURSFaaKJrAtjQ/a9rs3bZ/N29oSsFAnUXDA7atBCTZXptX2A0i4d2qRBG2vfl1HXyJkd+Y5G",
	"PnSp6noaKX9Pt/ut6FGO0z0s6KHrcsl3khnd+f1Ug8OATodnYS8gdvjldmq7rrCExTKwSKgxOkzGNkyX",
	"sDN5xelppZRBU3aK1EmUvQasgk0qgW5CCZ+fCeTr2umAKdCDRMViXsr/nhucfu+L146uIC2XJL9mKJpx",
	"HudLdZhRiEDsDLAnGyu8O384NM2Gr7X12qw4JuJ2IYtZk3ASmWLZWJVT19LKYUkcR5T/AQuiiM8ektDN",
	"Bj+Tv6Gj0HUqFbHPAkyzcgkBDvEUVL3mOeMQoBvCZznB6RMXQgY66iY1Zg5+EFbPgoEsJEwB+5LyLZM8",
	"/IHYqB5T7EYv+CDG7gzQo4djZ+Ak4lIkocn2R6Obm5uHWH5+GNHpSPdlo+Ojw2evzp4NHz0cP5zxwM+l",
	"GJv9a+sceplt8uDkyBk410CZri7ycPxwbHL3cEycfefxw/HDxyodfSZpYiSZaKR3M/Qjzkbv/Ygfebcj",
	"rWIPlWYum0/VBVhl8OlUAue7wA/EKLl8IWkmLOj9TFVmwCqj1Nl/q2lHrCRDrq+LG2YXD3V0ZxXTW81q",
	"etyvE5D13PTAJHT9xIOjMBMC6ZCtUeTng+IjBI/G415V3nsmQOmsuyqf31ZdqRLCqsDJVJvLGaLAKYHr",
	"cpm924GzN35ct450h6NCvf+98V57j7T8vpQ8SRBgOleEUSgdEluXK5CIBXm9dSQZoWFK3If5hs65OHoj",
	"ZqHAk4h9RCR4rjoD408ib7629wAaykzeFi/rYsG3FZrdXdtKqqTakTSNQdpCl+N2Ksu9/XEPUi5QpoIl",
	"CuHGTpQ9aPJ20EeIjt6rP468WyV6TLR1kaCfyt87kPShHm3L0rU4rpstYkWuKRDsnk2ttlGVguKHlnYK",
	"ZyvT02CVQ/abTA7jj0B+fUwnK4vBJRNx/1+Z4uLEdqgmv4MUt/5ju8EG3unY/hjI3lq1eTvH9mp8ooD/",
	"4c74EdbvMEnDwSq6q+Ez87DT//FbXUXoppevfms4ztDNbyHPGQSsk+tKDJaWQR/GqpZvF/YqspMZQRcD",
	"djZDjI0Vl7dMjM1FkG3v/gEgN+uBNKxz6Wof+hqnNyGL3OdXKu18hvyoLhG9OtldK3sw9Ka2z03HzVBZ",
	"yb+9Vaqy2MgbhJzEBcIUVOJwjmY+2GKu0z5I58OuSpUG26tRYMJnI+VeH+ZD1BvoLuGz4nNBGyI3+5tE",
	"W6a7cnlaG55NYLuOUliPuNpt71J9mDMzPMmlZC8paU/uDEIu4ABeWnvA0Er2sUQcE1nGvQdxFOu+b+rE",
	"s1a13/ZRZ69w30QjKvlBxYzIPI2ViSXnhHP2357nqUC3ychAzt4J6yrIohXXxzoWYxMoLlTO3jJmC2/B",
	"WvAp15bPYbk/z9bgTs3QmUt13mE7xnRC5YZwZkvX/MhQJ9eGNLys0no10atBgNSDwcjkVXVBocoM7YJD",
	"3XJTSCymqG7Z7dOGQFnQ3gBrXcftd9q7ZM+jN4pctS6EpdenB/8yCL1C4GMXOiinS26MIuryMj8+dSy/",
	"yu0dtDLJFKqPZJk3sToRQB8tq5CKuUG0f2gdy55y2q5ilc/mbangDRSSU8QSGffZ/WzIZ6nU+u0SPjP5",
	"Ls4GMVLJqbEgw7Rp9GStds5+F7jJcFbBNohlm+8OUaHpRgkfYt9vZzqzrWPZ58D3nQ8r6w58H0Uyxcts",
	"B3GgAQlrzGarQVztWr01hH0/mxPeuRBzk1rWD/zv02qxXSIVcjg4S6vMdnFRsFzrj9Nb3AHdZ6ZcyVpx",
	"vJoZPk8Uqas4l1zYSgnyuJwP0wjyJvEmz3YdiV3B+r0frLQF8hnRXE8r26SNNpU4v9kPaoCqOf0U3oqv",
	"WOkzMMNEt+NQP/PSeBI+MW3skqGEah3VPuiIi9Ir8XWRoEnsRoFKwPrYQkD1DrpEfhpI1p7jdUfyZYYC",
	"g84UK80BljnkbS68sfS4+5ZvuCkCagG+3gjG3pfbcgCjusxqnNpRmufN0Xv9V6dD3QzyxPTpdKBf5lp/",
	"nAd6FyybJPb13E4ztMlxEW5G2aBVgP4OokS/Jt14cVktoO6yOFM9O810dssoe8Ky8YZSed92QyK09h3d",
	"LZsGqk/lW/CaPc+qk2zWxWyZqvO+lK709vy2aMq35/8oTdQgWb+IOjLPnhqaMENrmihmBTfoQK9K6cMd",
	"FKEkpIC9fgpLjfajnuuxjvTIUkRzO3pP4anKDspPAYS9NaBK/rZGaBExFqyO3uf/PfJuRwGmV7KmjS7X",
	"6s4sAkD8XBj7VWGUl5henSr0tkvy4gI2Ic7rIY0C+QwkwgLkeOX7qdh3ARfpwM0oyUdgNvFZLpyy7Up6",
	"rN/U01nsar7hJZYiSb56WHMZ9XGRl1oLnwm2LDss9QN9/Scv3WHuMfmZftIReyRh2XuPNROqZnbhsTse",
	"28RHjRRiEa0RQtm7lFmmdO4nWXTYkqtb3dpz4nOg6HKO0ico7bvKfbaIrdbU23uJ2eyt8T5Q04+gWUfd",
	"hOzuFtnKmm3/afZfR0GtSTKXNpiX0WY8ydVVeWAisjvKheOIby1xYTvIaMHBFpTouDpbf/yNcOl9za7I",
	"LLzL+U1CbPEpYIvDIfcdkVAtuiljqYK4LLUdF4HYiDuZJj/U78s0YypX7GRlN1jHjOp8Degu6dSl53L6",
	"apYMi7alR3cKEFRfDBCa7W3rhViz47BaBM/q5chXuFuXHcxm1rKVKJORkp4nKFBZvapl1WrBbKHVkap/",
	"MnR13bfmG3wRF2p5pmLchi7zDbXptp7FUuCjNr7RaEtrQq6ZRF7g0PNLJKLnkhnmGVb60YMJ7h+qt6U6",
	"izIT7X2mn6TaNBrqHkRrwIsCUhboXhevTibZ016VF8o4SmL7y169wPy+VDewgx26CO9y1awup3xc6bOK",
	"+3Cv9XE9CkG0buXqVI6JsPWUWR0NIwZ86GUVLrvKwhI6zoCbMpnbR8w6LZjVCm3Nxkzru371ebP3p4Mz",
	"qEyTqzVaXxmuC3l00eA62iz7Gho/kjtvH8Wyj0o5I4xHdN5bpYyL/auILGNwdAOXsyi6MgpOJ15mX+hO",
	"6lRdQaMpFyLrqqiU6mOr5dR6BhoiGrReoKGAIPTiiBRin+oAlwt4Gint1pxyQxmP0gmQafCTrtaTPer7",
	"DYmGsj2f3ED3Mn5kG3eF/EvUhSekdopPVT/oSQmaBIeFpzn7kcKJGuIgq1G6bVrYQJZ9tpkPl06SraAh",
	"ErOuvuu6YjgerZu1is/KN/CWSVft5/TU1GgpfCu4Jndrq4QKltilS+hzLux5/QFfpfc61uXy/G3SRMwz",
	"QJ00EWW+Y20R4PWqSHGAHGWkaC5Sxginz+K2EciBKcv4UcFMraqy896gw/ZxOkCwFIXdBsZvWux1iqtW",
	"et6ia6M0Yz0WBfbZSEaXNErJN6LdiWq2lfuLeUOpjf4LATL9yb5QX7dwCVU/NNu/y1DZWEVNMcWHqqSp",
	"MNECefVu/4cOOT3wPG15L+DVhtYy8Y/e69e6Olj7cmg/0U98dbIkpW03XIKyiJmNWvr6wJlGE+JDu5TR",
	"7TYos+ULDXXpwXqdG5TSSW6aHMzk7huLHVbgs6lagPlnZrZ8aarDjd72+gr92arwtWCmco/J16vXtxig",
	"1/YQrhMaeYmK3lSNKrXWcUweZvXkR9e7jiUeiuOpOuGtQzD1edhlqKdwDX4Ua/9LZbj90ciPXOzPIsb3",
	"Px1/OhbrkyOdp5B5b2MgXMi9kUXpTa6PSTQ3deeLWTrVJcrxckXyU45g2SAKOZawtYIUzAbJemohVe2a",
	"j0fJRyqIraQBb2aQfMxB/VBGI7OtI9XJqt3NRVm6mKe68p6xGOaAkIb62lZQeTRG7CMz16Yb0Xfn+k1Q",
	"EHRSRkAael6Dv3I0q+5WjJys9lUlqNK9yrt/a6l2K3ybi1ndnt/+bwAAAP//X0DpOdC/AAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
