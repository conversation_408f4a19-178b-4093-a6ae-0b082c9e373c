package email

import (
	"fmt"
	"net/smtp"
	"strings"
)

type EmailService interface {
	SendVerificationEmail(to, token string) error
	SendPasswordResetEmail(to, token string) error
	SendWelcomeEmail(to, name string) error
	SendPaymentConfirmationEmail(to, paymentID string, amount float64) error
}

type emailService struct {
	smtpHost     string
	smtpPort     string
	smtpUsername string
	smtpPassword string
	fromAddress  string
	fromName     string
}

type EmailConfig struct {
	SMTPHost     string
	SMTPPort     string
	SMTPUsername string
	SMTPPassword string
	FromAddress  string
	FromName     string
}

func NewEmailService(config EmailConfig) EmailService {
	return &emailService{
		smtpHost:     config.SMTPHost,
		smtpPort:     config.SMTPPort,
		smtpUsername: config.SMTPUsername,
		smtpPassword: config.SMTPPassword,
		fromAddress:  config.FromAddress,
		fromName:     config.FromName,
	}
}

func (e *emailService) SendVerificationEmail(to, token string) error {
	subject := "Email Verification - Smooth Parking"
	body := fmt.Sprintf(`
		<html>
		<body>
			<h2>Email Verification</h2>
			<p>Please click the link below to verify your email address:</p>
			<a href="https://app.smooth-parking.com/verify?token=%s">Verify Email</a>
			<p>If you didn't request this verification, please ignore this email.</p>
		</body>
		</html>
	`, token)

	return e.sendEmail(to, subject, body, true)
}

func (e *emailService) SendPasswordResetEmail(to, token string) error {
	subject := "Password Reset - Smooth Parking"
	body := fmt.Sprintf(`
		<html>
		<body>
			<h2>Password Reset</h2>
			<p>You have requested to reset your password. Click the link below to reset it:</p>
			<a href="https://app.smooth-parking.com/reset-password?token=%s">Reset Password</a>
			<p>This link will expire in 1 hour.</p>
			<p>If you didn't request this password reset, please ignore this email.</p>
		</body>
		</html>
	`, token)

	return e.sendEmail(to, subject, body, true)
}

func (e *emailService) SendWelcomeEmail(to, name string) error {
	subject := "Welcome to Smooth Parking!"
	body := fmt.Sprintf(`
		<html>
		<body>
			<h2>Welcome to Smooth Parking, %s!</h2>
			<p>Thank you for joining Smooth Parking. Your account has been successfully created.</p>
			<p>You can now:</p>
			<ul>
				<li>Add your vehicles and license plates</li>
				<li>Set up payment methods for automatic payments</li>
				<li>View your parking history</li>
				<li>Manage your account settings</li>
			</ul>
			<p>If you have any questions, feel free to contact our support team.</p>
			<p>Best regards,<br>The Smooth Parking Team</p>
		</body>
		</html>
	`, name)

	return e.sendEmail(to, subject, body, true)
}

func (e *emailService) SendPaymentConfirmationEmail(to, paymentID string, amount float64) error {
	subject := "Payment Confirmation - Smooth Parking"
	body := fmt.Sprintf(`
		<html>
		<body>
			<h2>Payment Confirmation</h2>
			<p>Your payment has been successfully processed.</p>
			<p><strong>Payment ID:</strong> %s</p>
			<p><strong>Amount:</strong> $%.2f</p>
			<p>Thank you for using Smooth Parking!</p>
		</body>
		</html>
	`, paymentID, amount)

	return e.sendEmail(to, subject, body, true)
}

func (e *emailService) sendEmail(to, subject, body string, isHTML bool) error {
	from := e.fromAddress
	auth := smtp.PlainAuth("", e.smtpUsername, e.smtpPassword, e.smtpHost)

	contentType := "text/plain"
	if isHTML {
		contentType = "text/html"
	}

	msg := []string{
		fmt.Sprintf("From: %s <%s>", e.fromName, from),
		fmt.Sprintf("To: %s", to),
		fmt.Sprintf("Subject: %s", subject),
		fmt.Sprintf("Content-Type: %s; charset=UTF-8", contentType),
		"",
		body,
	}

	msgStr := strings.Join(msg, "\r\n")
	addr := fmt.Sprintf("%s:%s", e.smtpHost, e.smtpPort)

	return smtp.SendMail(addr, auth, from, []string{to}, []byte(msgStr))
} 