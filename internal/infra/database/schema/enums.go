package schema

import (
	"fmt"

	"gorm.io/gorm"
)

func CreateEnums(db *gorm.DB) error {
	if err := db.Exec(`
		-- User enums
		DO $$ BEGIN
			CREATE TYPE user_role AS ENUM ('user', 'admin');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		DO $$ BEGIN
			CREATE TYPE user_status AS ENUM ('active', 'suspended', 'pending');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		DO $$ BEGIN
			CREATE TYPE language_code AS ENUM ('ja', 'en');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		-- Verification token types
		DO $$ BEGIN
			CREATE TYPE verification_type AS ENUM ('email_verification', 'password_reset');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		-- Japanese plate types
		DO $$ BEGIN
			CREATE TYPE plate_type AS ENUM ('normal', 'commercial', 'rental', 'military');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		-- Parking lot types
		DO $$ BEGIN
			CREATE TYPE parking_lot_type AS ENUM ('outdoor', 'indoor', 'mechanical');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		-- Parking lot status
		DO $$ BEGIN
			CREATE TYPE parking_lot_status AS ENUM ('active', 'inactive', 'maintenance');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		-- Session status
		DO $$ BEGIN
			CREATE TYPE session_status AS ENUM ('active', 'completed', 'cancelled');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		-- Payment status
		DO $$ BEGIN
			CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		-- Payment method
		DO $$ BEGIN
			CREATE TYPE payment_method AS ENUM ('card', 'paypay');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		-- Booking status
		DO $$ BEGIN
			CREATE TYPE booking_status AS ENUM ('pending', 'confirmed', 'cancelled', 'completed');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		-- Notification types
		DO $$ BEGIN
			CREATE TYPE notification_type AS ENUM (
				'parked', 'payment_completed', 'overstay_warning', 
				'price_alert', 'booking_reminder'
			);
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;

		-- Detection direction
		DO $$ BEGIN
			CREATE TYPE detection_direction AS ENUM ('entry', 'exit');
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;
	`).Error; err != nil {
		return fmt.Errorf("failed to create enum types: %w", err)
	}

	return nil
}
