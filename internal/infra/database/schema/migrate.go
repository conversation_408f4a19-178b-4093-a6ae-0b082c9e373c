package schema

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/model"
)

func AutoMigrateModels(ctx context.Context, db *gorm.DB) error {
	if err := db.WithContext(ctx).AutoMigrate(
		&model.UserModel{},
		&model.UserSessionModel{},
		&model.VerificationTokenModel{},
		&model.PlateModel{},
		&model.ParkingLotModel{},
		&model.SessionModel{},
		&model.PaymentModel{},
		&model.PaymentMethodModel{},
		&model.BookingModel{},
		&model.NotificationModel{},
		&model.ParkingLotConfigModel{},
	); err != nil {
		return fmt.Errorf("failed to auto-migrate database models: %w", err)
	}

	return nil
}
