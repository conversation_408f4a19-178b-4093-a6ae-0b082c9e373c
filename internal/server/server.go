package server

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"

	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/controller"
	"github.com/smooth-inc/backend/internal/infra/http/middleware"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type Config struct {
	Port         int
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
	Environment  string
}

type Server struct {
	config           Config
	controller       *controller.Controller
	logger           *logger.Logger
	jwtService       usecase.JWTService
	httpServer       *http.Server
	rateLimiter      *middleware.RateLimiter
	metricsCollector *middleware.MetricsCollector
}

func New(config Config, controller *controller.Controller, logger *logger.Logger, jwtService usecase.JWTService) *Server {
	s := &Server{
		config:     config,
		controller: controller,
		logger:     logger,
		jwtService: jwtService,
	}

	s.setupComponents()
	s.setupServer()
	return s
}

func (s *Server) setupComponents() {
	var rateLimiterConfig middleware.RateLimiterConfig
	if s.config.Environment == "production" {
		rateLimiterConfig = middleware.StrictRateLimiterConfig()
	} else {
		rateLimiterConfig = middleware.DefaultRateLimiterConfig()
	}
	s.rateLimiter = middleware.NewRateLimiter(rateLimiterConfig)

	var metricsConfig middleware.MetricsConfig
	if s.config.Environment == "production" {
		metricsConfig = middleware.DefaultMetricsConfig()
	} else {
		metricsConfig = middleware.DetailedMetricsConfig()
	}
	s.metricsCollector = middleware.NewMetricsCollector(metricsConfig, s.logger)
}

func (s *Server) setupServer() {
	router := gin.New()

	s.setupMiddleware(router)
	s.setupRoutes(router)

	s.httpServer = &http.Server{
		Addr:         s.getAddress(),
		Handler:      router,
		ReadTimeout:  s.config.ReadTimeout,
		WriteTimeout: s.config.WriteTimeout,
		IdleTimeout:  s.config.IdleTimeout,
	}
}

func (s *Server) setupMiddleware(router *gin.Engine) {
	router.Use(middleware.RecoveryMiddleware(s.logger))

	router.Use(middleware.MetricsMiddleware(s.metricsCollector))

	var rateLimiterConfig middleware.RateLimiterConfig
	if s.config.Environment == "production" {
		rateLimiterConfig = middleware.StrictRateLimiterConfig()
	} else {
		rateLimiterConfig = middleware.DefaultRateLimiterConfig()
	}
	router.Use(middleware.RateLimitMiddleware(rateLimiterConfig))

	var validationConfig middleware.ValidationConfig
	if s.config.Environment == "production" {
		validationConfig = middleware.StrictValidationConfig()
	} else {
		validationConfig = middleware.DefaultValidationConfig()
	}
	router.Use(middleware.RequestValidationMiddleware(validationConfig, s.logger))

	router.Use(middleware.RequestLoggingMiddleware(s.logger))

	corsConfig := middleware.DefaultCORSConfig()
	if s.config.Environment == "production" {
		corsConfig.AllowOrigins = []string{
			"https://smooth.inc",
			"https://app.smooth.inc",
			"https://admin.smooth.inc",
		}
	}
	router.Use(middleware.CORSMiddleware(corsConfig))
	router.Use(middleware.TimeoutMiddleware(s.logger))

	router.GET("/metrics", s.metricsEndpoint)
}

func (s *Server) setupRoutes(router *gin.Engine) {
	v1 := router.Group("/api/v1")

	v1.GET("/health", s.healthCheck)

	authMiddleware := middleware.JWTAuthMiddleware(s.jwtService, s.logger)

	var apiKeyConfig middleware.APIKeyConfig
	if s.config.Environment == "production" {
		apiKeyConfig = middleware.SecureAPIKeyConfig()
	} else {
		apiKeyConfig = middleware.DefaultAPIKeyConfig()
	}
	apiKeyMiddleware := middleware.APIKeyAuthMiddleware(apiKeyConfig, s.logger)

	authRoutes := v1.Group("/auth")
	s.setupAuthRoutes(authRoutes)

	userRoutes := v1.Group("/users")
	userRoutes.Use(authMiddleware)
	s.setupUserRoutes(userRoutes)

	apiRoutes := v1.Group("/api")
	apiRoutes.Use(apiKeyMiddleware)
	s.setupAPIRoutes(apiRoutes)

	adminRoutes := v1.Group("/admin")
	adminRoutes.Use(authMiddleware)
	adminRoutes.Use(func(c *gin.Context) {
		userRole, exists := c.Get("user_role")
		if !exists || userRole != "admin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			c.Abort()
			return
		}
		c.Next()
	})
	s.setupAdminRoutes(adminRoutes)

	v1.GET("/status", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"service": "smooth-backend",
			"version": "1.0.0",
		})
	})

	s.logger.LogInfo(context.Background(), "Routes configured successfully")
}

func (s *Server) setupAuthRoutes(router *gin.RouterGroup) {
	router.POST("/register", s.controller.PostAuthRegister)
	router.POST("/login", s.controller.PostAuthLogin)
	router.POST("/refresh", s.controller.PostAuthRefresh)
	router.POST("/forgot-password", s.controller.PostAuthForgotPassword)
	router.POST("/reset-password", s.controller.PostAuthResetPassword)
	router.GET("/verify-email", func(c *gin.Context) {
		token := c.Query("token")
		params := api.GetAuthVerifyEmailParams{
			Token: token,
		}
		s.controller.GetAuthVerifyEmail(c, params)
	})
	router.POST("/resend-verification", s.controller.PostAuthResendVerification)

	authMiddleware := middleware.JWTAuthMiddleware(s.jwtService, s.logger)
	authenticatedAuth := router.Group("")
	authenticatedAuth.Use(authMiddleware)

	authenticatedAuth.POST("/change-password", s.controller.PostAuthChangePassword)
	authenticatedAuth.GET("/sessions", s.controller.GetAuthSessions)
	authenticatedAuth.DELETE("/sessions/:sessionId", func(c *gin.Context) {
		sessionIdStr := c.Param("sessionId")
		sessionId, err := uuid.Parse(sessionIdStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid session ID"})
			return
		}
		s.controller.DeleteAuthSessionsSessionId(c, types.UUID(sessionId))
	})
	authenticatedAuth.POST("/sessions/logout-all", s.controller.PostAuthSessionsLogoutAll)
}

func (s *Server) setupUserRoutes(router *gin.RouterGroup) {
	router.GET("/profile", s.controller.GetUsersProfile)
	router.PUT("/profile", s.controller.PutUsersProfile)
	router.GET("/plates", s.controller.GetUsersPlates)
	router.POST("/plates", s.controller.PostUsersPlates)
	router.DELETE("/plates/:plateId", func(c *gin.Context) {
		plateIdStr := c.Param("plateId")
		plateId, err := uuid.Parse(plateIdStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid plate ID"})
			return
		}
		s.controller.DeleteUsersPlatesPlateId(c, types.UUID(plateId))
	})
}

func (s *Server) setupAPIRoutes(router *gin.RouterGroup) {
	router.GET("/parking-spots", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "API key authenticated endpoint",
			"data":    []string{"spot1", "spot2", "spot3"},
		})
	})

	router.POST("/parking-spots/:spotId/reserve", func(c *gin.Context) {
		spotId := c.Param("spotId")
		c.JSON(http.StatusOK, gin.H{
			"message": "Parking spot reserved via API",
			"spot_id": spotId,
		})
	})

	router.GET("/analytics/usage", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Usage analytics",
			"data": gin.H{
				"total_spots": 100,
				"occupied":    75,
				"available":   25,
			},
		})
	})
}

func (s *Server) setupAdminRoutes(router *gin.RouterGroup) {
	router.GET("/users", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Admin endpoint - list users",
			"users":   []string{"user1", "user2", "user3"},
		})
	})

	router.POST("/parking-spots", func(c *gin.Context) {
		c.JSON(http.StatusCreated, gin.H{
			"message": "Admin endpoint - parking spot created",
		})
	})

	router.DELETE("/users/:userId", func(c *gin.Context) {
		userId := c.Param("userId")
		c.JSON(http.StatusOK, gin.H{
			"message": "Admin endpoint - user deleted",
			"user_id": userId,
		})
	})

	router.GET("/system/status", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "System status",
			"uptime":  time.Since(time.Now().Add(-time.Hour)),
			"version": "1.0.0",
		})
	})
}

func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"service":   "smooth-backend",
		"version":   "1.0.0",
	})
}

func (s *Server) metricsEndpoint(c *gin.Context) {
	if s.metricsCollector != nil {
		metrics := s.metricsCollector.GetMetrics()
		c.JSON(http.StatusOK, metrics)
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Metrics not available"})
	}
}

func (s *Server) getAddress() string {
	if s.config.Port == 0 {
		s.config.Port = 8080
	}
	return fmt.Sprintf(":%d", s.config.Port)
}

func (s *Server) GetHTTPServer() *http.Server {
	return s.httpServer
}

func (s *Server) GetRateLimiter() *middleware.RateLimiter {
	return s.rateLimiter
}

func (s *Server) GetMetricsCollector() *middleware.MetricsCollector {
	return s.metricsCollector
}
