package domain

import (
	"errors"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

type UserRole string

const (
	UserRoleUser  UserRole = "user"
	UserRoleAdmin UserRole = "admin"
)

type UserStatus string

const (
	UserStatusActive    UserStatus = "active"
	UserStatusSuspended UserStatus = "suspended"
	UserStatusPending   UserStatus = "pending"
)

type LanguageCode string

const (
	LanguageCodeJapanese LanguageCode = "ja"
	LanguageCodeEnglish  LanguageCode = "en"
)

var usernameRegex = regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)

type User struct {
	ID                     uuid.UUID
	Username               string
	Email                  string
	PasswordHash           string
	Name                   string
	Phone                  *string
	PreferredLanguage      LanguageCode
	Role                   UserRole
	Status                 UserStatus
	StripeCustomerID       *string
	DefaultPaymentMethodID *string
	AutoPaymentEnabled     bool
	NotifyEmail            bool
	NotifyPush             bool
	EmailVerified          bool
	LastLoginAt            *time.Time
	CreatedAt              time.Time
	UpdatedAt              time.Time
	DeletedAt              *time.Time
}

func NewUser(username, email, name, password string, preferredLanguage LanguageCode) (*User, error) {
	if err := ValidateUsername(username); err != nil {
		return nil, err
	}
	if err := ValidateEmail(email); err != nil {
		return nil, err
	}
	if err := ValidateName(name); err != nil {
		return nil, err
	}
	if err := ValidatePassword(password); err != nil {
		return nil, err
	}
	if err := ValidateLanguageCode(preferredLanguage); err != nil {
		return nil, err
	}

	hashedPassword, err := hashPassword(password)
	if err != nil {
		return nil, err
	}

	return &User{
		ID:                 uuid.New(),
		Username:           strings.ToLower(username),
		Email:              strings.ToLower(email),
		Name:               name,
		PasswordHash:       hashedPassword,
		PreferredLanguage:  preferredLanguage,
		Role:               UserRoleUser,
		Status:             UserStatusActive,
		AutoPaymentEnabled: true,
		NotifyEmail:        true,
		NotifyPush:         true,
		EmailVerified:      false,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}, nil
}

func (u *User) SetPassword(password string) error {
	if password == "" {
		return errors.New("password cannot be empty")
	}

	hashedPassword, err := hashPassword(password)
	if err != nil {
		return err
	}

	u.PasswordHash = hashedPassword
	u.UpdatedAt = time.Now()
	return nil
}

func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password))
	return err == nil
}

func (u *User) UpdateProfile(name, phone *string, autoPaymentEnabled, notifyEmail, notifyPush *bool) {
	if name != nil {
		u.Name = *name
	}
	if phone != nil {
		u.Phone = phone
	}
	if autoPaymentEnabled != nil {
		u.AutoPaymentEnabled = *autoPaymentEnabled
	}
	if notifyEmail != nil {
		u.NotifyEmail = *notifyEmail
	}
	if notifyPush != nil {
		u.NotifyPush = *notifyPush
	}
	u.UpdatedAt = time.Now()
}

func (u *User) UpdateExtendedProfile(name, phone *string, email, username *string, preferredLanguage *LanguageCode, defaultPaymentMethodID *string, autoPaymentEnabled, notifyEmail, notifyPush *bool) error {
	if name != nil {
		u.Name = *name
	}
	if phone != nil {
		u.Phone = phone
	}
	if email != nil {
		if err := ValidateEmail(*email); err != nil {
			return err
		}
		oldEmail := u.Email
		u.Email = strings.ToLower(*email)
		// If email changed, mark as unverified
		if oldEmail != u.Email {
			u.EmailVerified = false
		}
	}
	if username != nil {
		if err := ValidateUsername(*username); err != nil {
			return err
		}
		u.Username = strings.ToLower(*username)
	}
	if preferredLanguage != nil {
		if err := ValidateLanguageCode(*preferredLanguage); err != nil {
			return err
		}
		u.PreferredLanguage = *preferredLanguage
	}
	if defaultPaymentMethodID != nil {
		u.DefaultPaymentMethodID = defaultPaymentMethodID
	}
	if autoPaymentEnabled != nil {
		u.AutoPaymentEnabled = *autoPaymentEnabled
	}
	if notifyEmail != nil {
		u.NotifyEmail = *notifyEmail
	}
	if notifyPush != nil {
		u.NotifyPush = *notifyPush
	}
	u.UpdatedAt = time.Now()
	return nil
}

func (u *User) UpdateEmail(email string) error {
	if err := ValidateEmail(email); err != nil {
		return err
	}
	oldEmail := u.Email
	u.Email = strings.ToLower(email)
	if oldEmail != u.Email {
		u.EmailVerified = false
	}
	u.UpdatedAt = time.Now()
	return nil
}

func (u *User) SetStripeCustomerID(customerID string) {
	u.StripeCustomerID = &customerID
	u.UpdatedAt = time.Now()
}

func (u *User) Suspend() {
	u.Status = UserStatusSuspended
	u.UpdatedAt = time.Now()
}

func (u *User) Activate() {
	u.Status = UserStatusActive
	u.UpdatedAt = time.Now()
}

func (u *User) VerifyEmail() {
	u.EmailVerified = true
	u.UpdatedAt = time.Now()
}

func (u *User) RecordLogin() {
	now := time.Now()
	u.LastLoginAt = &now
	u.UpdatedAt = now
}

func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

func (u *User) IsAdmin() bool {
	return u.Role == UserRoleAdmin
}

func (u *User) CanCreatePlate() bool {
	return u.IsActive() && u.EmailVerified
}

func (u *User) CanLogin() bool {
	return u.IsActive() && u.EmailVerified
}

func (u *User) ChangePassword(currentPassword, newPassword string) error {
	if !u.CheckPassword(currentPassword) {
		return errors.New("current password is incorrect")
	}

	if err := ValidatePassword(newPassword); err != nil {
		return err
	}

	hashedPassword, err := hashPassword(newPassword)
	if err != nil {
		return err
	}

	u.PasswordHash = hashedPassword
	u.UpdatedAt = time.Now()
	return nil
}

func (u *User) UpdateUsername(username string) error {
	if err := ValidateUsername(username); err != nil {
		return err
	}
	u.Username = strings.ToLower(username)
	u.UpdatedAt = time.Now()
	return nil
}

func (u *User) SetDefaultPaymentMethod(paymentMethodID string) {
	u.DefaultPaymentMethodID = &paymentMethodID
	u.UpdatedAt = time.Now()
}

func (u *User) EnableAutoPayment() {
	u.AutoPaymentEnabled = true
	u.UpdatedAt = time.Now()
}

func (u *User) DisableAutoPayment() {
	u.AutoPaymentEnabled = false
	u.UpdatedAt = time.Now()
}

func ValidateUsername(username string) error {
	if username == "" {
		return errors.New("username is required")
	}
	if len(username) < 3 {
		return errors.New("username must be at least 3 characters long")
	}
	if len(username) > 50 {
		return errors.New("username must be at most 50 characters long")
	}
	if !usernameRegex.MatchString(username) {
		return errors.New("username can only contain letters, numbers, underscores, and hyphens")
	}
	return nil
}

func ValidateEmail(email string) error {
	if email == "" {
		return errors.New("email is required")
	}
	if len(email) > 255 {
		return errors.New("email must be at most 255 characters long")
	}
	// Basic email validation - more comprehensive validation should be done at the API layer
	if !strings.Contains(email, "@") || !strings.Contains(email, ".") {
		return errors.New("invalid email format")
	}
	return nil
}

func ValidateName(name string) error {
	if name == "" {
		return errors.New("name is required")
	}
	if len(name) > 200 {
		return errors.New("name must be at most 200 characters long")
	}
	return nil
}

func ValidatePassword(password string) error {
	if password == "" {
		return errors.New("password is required")
	}
	if len(password) < 8 {
		return errors.New("password must be at least 8 characters long")
	}
	return nil
}

func ValidateLanguageCode(lang LanguageCode) error {
	if lang != LanguageCodeJapanese && lang != LanguageCodeEnglish {
		return errors.New("invalid language code, must be 'ja' or 'en'")
	}
	return nil
}

func hashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}
