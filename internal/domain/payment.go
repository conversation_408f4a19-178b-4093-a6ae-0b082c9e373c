package domain

import (
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
)

type PaymentStatus string

const (
	PaymentStatusPending    PaymentStatus = "pending"
	PaymentStatusProcessing PaymentStatus = "processing"
	PaymentStatusCompleted  PaymentStatus = "completed"
	PaymentStatusFailed     PaymentStatus = "failed"
	PaymentStatusRefunded   PaymentStatus = "refunded"
)

type Payment struct {
	ID                    uuid.UUID
	SessionID             uuid.UUID
	UserID                uuid.UUID
	Amount                int
	Currency              string
	StripePaymentIntentID *string
	StripePaymentLinkID   *string
	StripeStatus          *string
	PaymentMethodType     *string
	CardLast4             *string
	CardBrand             *string
	Status                PaymentStatus
	PaidAt                *time.Time
	ReceiptURL            *string
	InvoiceNumber         *string
	FailureReason         *string
	RetryCount            int
	CreatedAt             time.Time
	UpdatedAt             time.Time
	DeletedAt             *time.Time
}

func NewPayment(sessionID, userID uuid.UUID, amount int) (*Payment, error) {
	if sessionID == uuid.Nil {
		return nil, errors.New("session ID is required")
	}
	if userID == uuid.Nil {
		return nil, errors.New("user ID is required")
	}
	if amount <= 0 {
		return nil, errors.New("amount must be positive")
	}

	return &Payment{
		ID:        uuid.New(),
		SessionID: sessionID,
		UserID:    userID,
		Amount:    amount,
		Currency:  "JPY",
		Status:    PaymentStatusPending,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}, nil
}

func (p *Payment) MarkAsProcessing() {
	p.Status = PaymentStatusProcessing
	p.UpdatedAt = time.Now()
}

func (p *Payment) MarkAsCompleted() {
	p.Status = PaymentStatusCompleted
	now := time.Now()
	p.PaidAt = &now
	p.UpdatedAt = now
}

func (p *Payment) MarkAsFailed(reason string) {
	p.Status = PaymentStatusFailed
	p.FailureReason = &reason
	p.RetryCount++
	p.UpdatedAt = time.Now()
}

func (p *Payment) MarkAsRefunded() {
	p.Status = PaymentStatusRefunded
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetStripePaymentIntentID(intentID string) {
	p.StripePaymentIntentID = &intentID
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetStripePaymentLinkID(linkID string) {
	p.StripePaymentLinkID = &linkID
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetStripeStatus(status string) {
	p.StripeStatus = &status
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetPaymentMethod(methodType, cardLast4, cardBrand string) {
	p.PaymentMethodType = &methodType
	p.CardLast4 = &cardLast4
	p.CardBrand = &cardBrand
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetReceiptURL(url string) {
	p.ReceiptURL = &url
	p.UpdatedAt = time.Now()
}

func (p *Payment) SetInvoiceNumber(invoiceNumber string) {
	p.InvoiceNumber = &invoiceNumber
	p.UpdatedAt = time.Now()
}

func (p *Payment) IsCompleted() bool {
	return p.Status == PaymentStatusCompleted
}

func (p *Payment) IsPending() bool {
	return p.Status == PaymentStatusPending
}

func (p *Payment) IsFailed() bool {
	return p.Status == PaymentStatusFailed
}

func (p *Payment) CanRetry() bool {
	return p.Status == PaymentStatusFailed && p.RetryCount < 3
}

func (p *Payment) GetAmountInCurrency() string {
	if p.Currency == "JPY" {
		return fmt.Sprintf("¥%d", p.Amount)
	}
	return fmt.Sprintf("%d %s", p.Amount, p.Currency)
}
