package domain

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

var (
	ErrPaymentMethodNotFound = errors.New("payment method not found")
)

type PaymentMethodType string

const (
	PaymentMethodTypeCard   PaymentMethodType = "card"
	PaymentMethodTypePayPay PaymentMethodType = "paypay"
)

type PaymentMethod struct {
	ID                    uuid.UUID
	UserID                uuid.UUID
	StripePaymentMethodID string
	Type                  PaymentMethodType
	Brand                 *string
	Last4                 *string
	ExpiryMonth           *int
	ExpiryYear            *int
	IsDefault             bool
	CreatedAt             time.Time
	UpdatedAt             time.Time
	DeletedAt             *time.Time
}

func NewPaymentMethod(userID uuid.UUID, stripePaymentMethodID string, methodType PaymentMethodType) (*PaymentMethod, error) {
	if userID == uuid.Nil {
		return nil, errors.New("user ID is required")
	}
	if stripePaymentMethodID == "" {
		return nil, errors.New("stripe payment method ID is required")
	}

	return &PaymentMethod{
		ID:                    uuid.New(),
		UserID:                userID,
		StripePaymentMethodID: stripePaymentMethodID,
		Type:                  methodType,
		IsDefault:             false,
		CreatedAt:             time.Now(),
		UpdatedAt:             time.Now(),
	}, nil
}

func (pm *PaymentMethod) SetCardDetails(brand, last4 string, expiryMonth, expiryYear int) {
	pm.Brand = &brand
	pm.Last4 = &last4
	pm.ExpiryMonth = &expiryMonth
	pm.ExpiryYear = &expiryYear
	pm.UpdatedAt = time.Now()
}

func (pm *PaymentMethod) SetAsDefault() {
	pm.IsDefault = true
	pm.UpdatedAt = time.Now()
}

func (pm *PaymentMethod) UnsetAsDefault() {
	pm.IsDefault = false
	pm.UpdatedAt = time.Now()
}

func (pm *PaymentMethod) IsExpired() bool {
	if pm.ExpiryYear == nil || pm.ExpiryMonth == nil {
		return false
	}
	now := time.Now()
	currentYear := now.Year()
	currentMonth := int(now.Month())

	if *pm.ExpiryYear < currentYear {
		return true
	}
	if *pm.ExpiryYear == currentYear && *pm.ExpiryMonth < currentMonth {
		return true
	}
	return false
}

func (pm *PaymentMethod) CanBeUsedForPayment() bool {
	return !pm.IsExpired() && pm.DeletedAt == nil
}

type SetupIntent struct {
	ID           string
	ClientSecret string
	Status       string
	CustomerID   string
	CreatedAt    time.Time
}

type PaymentMethodRequest struct {
	PaymentMethodID string
	SetupIntentID   string
	IsDefault       bool
}

func ValidatePaymentMethodType(methodType PaymentMethodType) error {
	switch methodType {
	case PaymentMethodTypeCard, PaymentMethodTypePayPay:
		return nil
	default:
		return errors.New("invalid payment method type")
	}
}
