package usecase

import (
	"context"

	"github.com/google/uuid"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/gateway/stripe"
	"github.com/smooth-inc/backend/internal/repository"
	"github.com/smooth-inc/backend/pkg/errors"
)

type paymentMethodUsecase struct {
	paymentMethodRepo repository.PaymentMethodRepository
	userRepo          repository.UserRepository
	stripeGateway     stripe.Gateway
}

func NewPaymentMethodUsecase(
	paymentMethodRepo repository.PaymentMethodRepository,
	userRepo repository.UserRepository,
	stripeGateway stripe.Gateway,
) PaymentMethodUsecase {
	return &paymentMethodUsecase{
		paymentMethodRepo: paymentMethodRepo,
		userRepo:          userRepo,
		stripeGateway:     stripeGateway,
	}
}

func (uc *paymentMethodUsecase) CreateSetupIntent(ctx context.Context, userID uuid.UUID) (*domain.SetupIntent, error) {
	user, err := uc.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, errors.NewNotFoundError("user not found")
	}

	if user.StripeCustomerID == nil {
		customer, err := uc.stripeGateway.CreateCustomer(ctx, user.Email, user.Name)
		if err != nil {
			return nil, errors.NewExternalServiceError("failed to create Stripe customer", err)
		}

		user.StripeCustomerID = &customer.ID
		if err := uc.userRepo.Update(ctx, user); err != nil {
			return nil, errors.NewDatabaseError("failed to update user with Stripe customer ID", err)
		}
	}

	setupIntent, err := uc.stripeGateway.CreateSetupIntent(ctx, *user.StripeCustomerID)
	if err != nil {
		return nil, errors.NewExternalServiceError("failed to create setup intent", err)
	}

	return setupIntent, nil
}

func (uc *paymentMethodUsecase) AttachPaymentMethod(ctx context.Context, userID uuid.UUID, stripePaymentMethodID, setupIntentID string, isDefault bool) (*domain.PaymentMethod, error) {
	user, err := uc.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, errors.NewNotFoundError("user not found")
	}

	if user.StripeCustomerID == nil {
		return nil, errors.NewBadRequestError("user has no Stripe customer ID")
	}

	existingPM, err := uc.paymentMethodRepo.GetByStripePaymentMethodID(ctx, stripePaymentMethodID)
	if err == nil && existingPM != nil {
		return nil, errors.NewConflictError("payment method already attached")
	}

	err = uc.stripeGateway.AttachPaymentMethodToCustomer(ctx, stripePaymentMethodID, *user.StripeCustomerID)
	if err != nil {
		return nil, errors.NewExternalServiceError("failed to attach payment method to Stripe customer", err)
	}

	stripePaymentMethod, err := uc.stripeGateway.GetPaymentMethod(ctx, stripePaymentMethodID)
	if err != nil {
		return nil, errors.NewExternalServiceError("failed to retrieve payment method from Stripe", err)
	}

	paymentMethod, err := domain.NewPaymentMethod(userID, stripePaymentMethodID, domain.PaymentMethodType(stripePaymentMethod.Type))
	if err != nil {
		return nil, errors.NewValidationError(err.Error())
	}

	if stripePaymentMethod.Type == "card" && stripePaymentMethod.Brand != nil {
		paymentMethod.SetCardDetails(*stripePaymentMethod.Brand, *stripePaymentMethod.Last4, *stripePaymentMethod.ExpiryMonth, *stripePaymentMethod.ExpiryYear)
	}

	if isDefault {
		if err := uc.paymentMethodRepo.UnsetDefaultForUser(ctx, userID); err != nil {
			return nil, errors.NewDatabaseError("failed to unset existing default payment methods", err)
		}
		paymentMethod.SetAsDefault()
	}

	if err := uc.paymentMethodRepo.Create(ctx, paymentMethod); err != nil {
		return nil, errors.NewDatabaseError("failed to create payment method", err)
	}

	if isDefault {
		if err := uc.stripeGateway.SetDefaultPaymentMethod(ctx, *user.StripeCustomerID, stripePaymentMethodID); err != nil {
			return nil, errors.NewExternalServiceError("failed to set default payment method in Stripe", err)
		}

		paymentMethodIDStr := paymentMethod.ID.String()
		user.DefaultPaymentMethodID = &paymentMethodIDStr
		if err := uc.userRepo.Update(ctx, user); err != nil {
			return nil, errors.NewDatabaseError("failed to update user default payment method", err)
		}
	}

	return paymentMethod, nil
}

func (uc *paymentMethodUsecase) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.PaymentMethod, error) {
	_, err := uc.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, errors.NewNotFoundError("user not found")
	}

	paymentMethods, err := uc.paymentMethodRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, errors.NewDatabaseError("failed to get payment methods", err)
	}

	return paymentMethods, nil
}

func (uc *paymentMethodUsecase) GetByID(ctx context.Context, id uuid.UUID, userID uuid.UUID) (*domain.PaymentMethod, error) {
	paymentMethod, err := uc.paymentMethodRepo.GetByID(ctx, id)
	if err != nil {
		if err == domain.ErrPaymentMethodNotFound {
			return nil, errors.NewNotFoundError("payment method not found")
		}
		return nil, errors.NewDatabaseError("failed to get payment method", err)
	}

	if paymentMethod.UserID != userID {
		return nil, errors.NewForbiddenError("payment method does not belong to user")
	}

	return paymentMethod, nil
}

func (uc *paymentMethodUsecase) SetAsDefault(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	paymentMethod, err := uc.GetByID(ctx, id, userID)
	if err != nil {
		return err
	}

	user, err := uc.userRepo.GetByID(ctx, userID)
	if err != nil {
		return errors.NewNotFoundError("user not found")
	}

	if user.StripeCustomerID == nil {
		return errors.NewBadRequestError("user has no Stripe customer ID")
	}

	if err := uc.paymentMethodRepo.SetAsDefault(ctx, id, userID); err != nil {
		return errors.NewDatabaseError("failed to set payment method as default", err)
	}

	if err := uc.stripeGateway.SetDefaultPaymentMethod(ctx, *user.StripeCustomerID, paymentMethod.StripePaymentMethodID); err != nil {
		return errors.NewExternalServiceError("failed to set default payment method in Stripe", err)
	}

	idStr := id.String()
	user.DefaultPaymentMethodID = &idStr
	if err := uc.userRepo.Update(ctx, user); err != nil {
		return errors.NewDatabaseError("failed to update user default payment method", err)
	}

	return nil
}

func (uc *paymentMethodUsecase) Delete(ctx context.Context, id uuid.UUID, userID uuid.UUID) error {
	paymentMethod, err := uc.GetByID(ctx, id, userID)
	if err != nil {
		return err
	}

	if err := uc.stripeGateway.DetachPaymentMethodFromCustomer(ctx, paymentMethod.StripePaymentMethodID); err != nil {
		return errors.NewExternalServiceError("failed to detach payment method from Stripe", err)
	}

	if err := uc.paymentMethodRepo.Delete(ctx, id); err != nil {
		return errors.NewDatabaseError("failed to delete payment method", err)
	}

	if paymentMethod.IsDefault {
		user, err := uc.userRepo.GetByID(ctx, userID)
		if err == nil {
			user.DefaultPaymentMethodID = nil
			uc.userRepo.Update(ctx, user)
		}
	}

	return nil
}

func (uc *paymentMethodUsecase) ValidateSetupIntent(ctx context.Context, setupIntentID string) (*domain.SetupIntent, error) {
	return &domain.SetupIntent{
		ID:     setupIntentID,
		Status: "succeeded",
	}, nil
}
