package usecase

import (
	"context"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

// JWTService provides JWT token operations
type JWTService interface {
	GenerateTokenPair(user *domain.User, sessionID uuid.UUID) (*domain.TokenPair, error)
	ValidateAccessToken(tokenString string) (*domain.JWTClaims, error)
	ValidateRefreshToken(tokenString string) (*domain.JWTClaims, error)
}

// EmailService provides basic email sending capabilities
type EmailService interface {
	SendEmail(to, subject, body string) error
	SendTemplatedEmail(template string, to string, data map[string]interface{}) error
}

// EmailTemplateService provides higher-level email functionality using templates
type EmailTemplateService interface {
	EmailService

	// SendEmailVerification sends email verification with token
	SendEmailVerification(user *domain.User, token string) error

	// SendPasswordReset sends password reset email with token
	SendPasswordReset(user *domain.User, token string) error

	// SendWelcomeEmail sends welcome email to new user
	SendWelcomeEmail(user *domain.User) error

	// SendEmailChangeVerification sends verification email for email change
	SendEmailChangeVerification(user *domain.User, newEmail, token string) error
}

type AuthUsecase interface {
	Register(ctx context.Context, req *domain.RegisterRequest) (*domain.User, error)
	Login(ctx context.Context, req *domain.LoginRequest, ipAddress, userAgent string) (*domain.AuthResponse, error)
	RefreshToken(ctx context.Context, req *domain.RefreshTokenRequest, ipAddress, userAgent string) (*domain.TokenPair, error)

	VerifyEmail(ctx context.Context, req *domain.EmailVerificationRequest) (*domain.AuthResponse, error)
	ResendEmailVerification(ctx context.Context, req *domain.ResendVerificationRequest) error

	ForgotPassword(ctx context.Context, req *domain.ForgotPasswordRequest) error
	ResetPassword(ctx context.Context, req *domain.ResetPasswordRequest) error
	ChangePassword(ctx context.Context, userID uuid.UUID, req *domain.ChangePasswordRequest) error

	GetUserSessions(ctx context.Context, userID uuid.UUID, currentSessionID uuid.UUID) (*domain.SessionsResponse, error)
	LogoutSession(ctx context.Context, userID uuid.UUID, sessionID uuid.UUID) error
	LogoutAllOtherSessions(ctx context.Context, userID uuid.UUID, currentSessionID uuid.UUID) error
}

type UserUsecase interface {
	GetByID(ctx context.Context, id uuid.UUID) (*domain.User, error)
	GetByEmail(ctx context.Context, email string) (*domain.User, error)
	UpdateProfile(ctx context.Context, userID uuid.UUID, name, phone *string, autoPaymentEnabled, notifyEmail, notifyPush *bool) (*domain.User, error)
	UpdateExtendedProfile(ctx context.Context, userID uuid.UUID, name, phone *string, email, username *string, preferredLanguage *domain.LanguageCode, defaultPaymentMethodID *string, autoPaymentEnabled, notifyEmail, notifyPush *bool) (*domain.User, error)
	List(ctx context.Context, limit, offset int) ([]*domain.User, error)
}

type PlateUsecase interface {
	Create(ctx context.Context, userID uuid.UUID, region, classification, hiragana, serialNumber string, plateType domain.PlateType) (*domain.Plate, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.Plate, error)
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Plate, error)
	Delete(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
}

type ParkingLotUsecase interface {
	GetByID(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, error)
	List(ctx context.Context, limit, offset int) ([]*domain.ParkingLot, error)
	SearchNearby(ctx context.Context, lat, lng float64, radius int, limit, offset int) ([]*domain.ParkingLot, error)
	GetAvailability(ctx context.Context, lotID uuid.UUID) (int, error)
}

type SessionUsecase interface {
	Start(ctx context.Context, parkingLotID uuid.UUID, plateID *uuid.UUID, userID *uuid.UUID) (*domain.Session, error)
	Complete(ctx context.Context, sessionID uuid.UUID, exitTime string) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Session, error)
	GetActiveByPlateID(ctx context.Context, plateID uuid.UUID) (*domain.Session, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Session, error)
}

type PaymentUsecase interface {
	CreatePaymentLink(ctx context.Context, sessionID uuid.UUID) (string, error)
	ProcessWebhook(ctx context.Context, payload []byte, signature string) error
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Payment, error)
	ProcessAutoPayment(ctx context.Context, sessionID uuid.UUID) (*domain.Payment, error)
}

type PaymentMethodUsecase interface {
	CreateSetupIntent(ctx context.Context, userID uuid.UUID) (*domain.SetupIntent, error)
	AttachPaymentMethod(ctx context.Context, userID uuid.UUID, stripePaymentMethodID, setupIntentID string, isDefault bool) (*domain.PaymentMethod, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.PaymentMethod, error)
	GetByID(ctx context.Context, id uuid.UUID, userID uuid.UUID) (*domain.PaymentMethod, error)
	SetAsDefault(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
	Delete(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
	ValidateSetupIntent(ctx context.Context, setupIntentID string) (*domain.SetupIntent, error)
}

type BookingUsecase interface {
	Create(ctx context.Context, userID, plateID, parkingLotID uuid.UUID, startTime, endTime string) (*domain.Booking, error)
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Booking, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Booking, error)
	Cancel(ctx context.Context, bookingID uuid.UUID, userID uuid.UUID) error
}

type NotificationUsecase interface {
	Create(ctx context.Context, userID uuid.UUID, notificationType domain.NotificationType, title, message string) (*domain.Notification, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, unreadOnly bool, limit, offset int) ([]*domain.Notification, error)
	MarkAsRead(ctx context.Context, notificationID uuid.UUID, userID uuid.UUID) error
	SendPendingNotifications(ctx context.Context) error
}

type HardwareUsecase interface {
	ProcessDetection(ctx context.Context, plateNumber string, parkingLotID uuid.UUID, confidence float64, imageURL *string) error
}

type UseCases struct {
	Auth          AuthUsecase
	User          UserUsecase
	Plate         PlateUsecase
	ParkingLot    ParkingLotUsecase
	Session       SessionUsecase
	Payment       PaymentUsecase
	PaymentMethod PaymentMethodUsecase
	Booking       BookingUsecase
	Notification  NotificationUsecase
	Hardware      HardwareUsecase
}
