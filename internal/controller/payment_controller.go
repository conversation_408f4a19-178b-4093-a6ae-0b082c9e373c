package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"
	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type PaymentController struct {
	paymentUsecase       usecase.PaymentUsecase
	paymentMethodUsecase usecase.PaymentMethodUsecase
	logger               *logger.Logger
}

func NewPaymentController(paymentUsecase usecase.PaymentUsecase, paymentMethodUsecase usecase.PaymentMethodUsecase, logger *logger.Logger) *PaymentController {
	return &PaymentController{
		paymentUsecase:       paymentUsecase,
		paymentMethodUsecase: paymentMethodUsecase,
		logger:               logger,
	}
}

func (pc *PaymentController) GetPayments(c *gin.Context, params api.GetPaymentsParams) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get payments not implemented yet"})
}

func (pc *PaymentController) PostPaymentsWebhooksStripe(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Stripe webhook not implemented yet"})
}

func (pc *PaymentController) PostPaymentsSessionIdCreatePaymentLink(c *gin.Context, sessionId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Create payment link not implemented yet"})
}

func (pc *PaymentController) PostPaymentsSessionIdProcessAutoPayment(c *gin.Context, sessionId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Process auto payment not implemented yet"})
}

func (pc *PaymentController) GetPaymentMethods(c *gin.Context) {
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	paymentMethods, err := pc.paymentMethodUsecase.GetByUserID(c.Request.Context(), userID)
	if err != nil {
		pc.logger.Error("Failed to get payment methods", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get payment methods"})
		return
	}

	var apiPaymentMethods []api.PaymentMethod
	for _, pm := range paymentMethods {
		idStr := pm.ID.String()
		typeStr := string(pm.Type)
		apiPM := api.PaymentMethod{
			Id:        &idStr,
			Type:      (*api.PaymentMethodType)(&typeStr),
			IsDefault: &pm.IsDefault,
			CreatedAt: &pm.CreatedAt,
			UpdatedAt: &pm.UpdatedAt,
		}

		if pm.Brand != nil {
			apiPM.Brand = pm.Brand
		}
		if pm.Last4 != nil {
			apiPM.Last4 = pm.Last4
		}
		if pm.ExpiryMonth != nil {
			apiPM.ExpiryMonth = pm.ExpiryMonth
		}
		if pm.ExpiryYear != nil {
			apiPM.ExpiryYear = pm.ExpiryYear
		}

		apiPaymentMethods = append(apiPaymentMethods, apiPM)
	}

	c.JSON(http.StatusOK, apiPaymentMethods)
}

func (pc *PaymentController) PostPaymentMethods(c *gin.Context) {
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	setupIntent, err := pc.paymentMethodUsecase.CreateSetupIntent(c.Request.Context(), userID)
	if err != nil {
		pc.logger.Error("Failed to create setup intent", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create setup intent"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"id":            setupIntent.ID,
		"client_secret": setupIntent.ClientSecret,
		"status":        setupIntent.Status,
	})
}

func (pc *PaymentController) DeletePaymentMethodsPaymentMethodId(c *gin.Context, paymentMethodId string) {
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	paymentMethodUUID, err := uuid.Parse(paymentMethodId)
	if err != nil {
		response.BadRequest(c, "INVALID_ID", "Invalid payment method ID", nil)
		return
	}

	err = pc.paymentMethodUsecase.Delete(c.Request.Context(), paymentMethodUUID, userID)
	if err != nil {
		pc.logger.Error("Failed to delete payment method", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete payment method"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Payment method deleted successfully"})
}

func (pc *PaymentController) PostPaymentMethodsPaymentMethodIdSetDefault(c *gin.Context, paymentMethodId string) {
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	paymentMethodUUID, err := uuid.Parse(paymentMethodId)
	if err != nil {
		response.BadRequest(c, "INVALID_ID", "Invalid payment method ID", nil)
		return
	}

	err = pc.paymentMethodUsecase.SetAsDefault(c.Request.Context(), paymentMethodUUID, userID)
	if err != nil {
		pc.logger.Error("Failed to set default payment method", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to set default payment method"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Payment method set as default successfully"})
}

func (pc *PaymentController) PostPaymentMethodsStripeCallback(c *gin.Context) {
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, "AUTH_REQUIRED", "User not authenticated")
		return
	}

	var req struct {
		PaymentMethodID string `json:"payment_method_id" binding:"required"`
		SetupIntentID   string `json:"setup_intent_id" binding:"required"`
		IsDefault       bool   `json:"is_default"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", err.Error())
		return
	}

	paymentMethod, err := pc.paymentMethodUsecase.AttachPaymentMethod(
		c.Request.Context(),
		userID,
		req.PaymentMethodID,
		req.SetupIntentID,
		req.IsDefault,
	)
	if err != nil {
		pc.logger.Error("Failed to attach payment method", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to attach payment method"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"id":         paymentMethod.ID.String(),
		"type":       string(paymentMethod.Type),
		"is_default": paymentMethod.IsDefault,
		"brand":      paymentMethod.Brand,
		"last4":      paymentMethod.Last4,
	})
}

func (pc *PaymentController) GetPaymentMethodsValidateSetup(c *gin.Context) {
	setupIntentID := c.Query("setup_intent_id")
	if setupIntentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "setup_intent_id parameter is required"})
		return
	}

	setupIntent, err := pc.paymentMethodUsecase.ValidateSetupIntent(c.Request.Context(), setupIntentID)
	if err != nil {
		pc.logger.Error("Failed to validate setup intent", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate setup intent"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"id":     setupIntent.ID,
		"status": setupIntent.Status,
		"valid":  setupIntent.Status == "succeeded",
	})
}
