package controller

import (
	"errors"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	openapi_types "github.com/oapi-codegen/runtime/types"

	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type AuthController struct {
	authUsecase usecase.AuthUsecase
	logger      *logger.Logger
}

func NewAuthController(authUsecase usecase.AuthUsecase, logger *logger.Logger) *AuthController {
	return &AuthController{
		authUsecase: authUsecase,
		logger:      logger,
	}
}

func (ac *AuthController) PostAuthRegister(c *gin.Context) {
	var req api.RegisterRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Invalid registration request format", map[string]interface{}{
			"error": err.Error(),
			"ip":    c.ClientIP(),
		})
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request format", nil)
		return
	}

	domainReq := &domain.RegisterRequest{
		Username:          req.Username,
		Email:             string(req.Email),
		Password:          req.Password,
		Name:              req.Name,
		Phone:             req.Phone,
		PreferredLanguage: domain.LanguageCode(req.PreferredLanguage),
	}

	ac.logger.LogInfo(c.Request.Context(), "Processing user registration", map[string]interface{}{
		"username": req.Username,
		"email":    string(req.Email),
		"ip":       c.ClientIP(),
	})

	user, err := ac.authUsecase.Register(c.Request.Context(), domainReq)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to register user", map[string]interface{}{
			"username": req.Username,
			"email":    string(req.Email),
		})
		ac.handleError(c, err)
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "User registered successfully", map[string]interface{}{
		"user_id":  user.ID,
		"username": user.Username,
		"email":    user.Email,
	})

	messageResponse := api.MessageResponse{
		Message: stringPtr("Registration successful. Please check your email for verification."),
		Success: boolPtr(true),
	}
	response.Created(c, messageResponse)
}

func (ac *AuthController) PostAuthLogin(c *gin.Context) {
	var req api.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Invalid login request format", map[string]interface{}{
			"error": err.Error(),
			"ip":    c.ClientIP(),
		})
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request format", nil)
		return
	}

	domainReq := &domain.LoginRequest{
		Identifier: req.Identifier,
		Password:   req.Password,
		DeviceID:   "",
		DeviceName: "",
		DeviceType: "",
	}

	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	ac.logger.LogInfo(c.Request.Context(), "Processing user login", map[string]interface{}{
		"identifier": req.Identifier,
		"ip":         ipAddress,
		"user_agent": userAgent,
	})

	authResponse, err := ac.authUsecase.Login(c.Request.Context(), domainReq, ipAddress, userAgent)
	if err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Login failed", map[string]interface{}{
			"identifier": req.Identifier,
			"ip":         ipAddress,
			"error":      err.Error(),
		})
		ac.handleError(c, err)
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "User logged in successfully", map[string]interface{}{
		"user_id":  authResponse.User.ID,
		"username": authResponse.User.Username,
		"email":    authResponse.User.Email,
	})

	apiResponse := api.AuthResponse{
		User:         ac.convertUserToAPI(authResponse.User),
		AccessToken:  &authResponse.Tokens.AccessToken,
		RefreshToken: &authResponse.Tokens.RefreshToken,
	}

	response.Success(c, apiResponse)
}

func (ac *AuthController) PostAuthRefresh(c *gin.Context) {
	var req api.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Invalid refresh token request format", map[string]interface{}{
			"error": err.Error(),
			"ip":    c.ClientIP(),
		})
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request format", nil)
		return
	}

	domainReq := &domain.RefreshTokenRequest{
		RefreshToken: req.RefreshToken,
		DeviceID:     getStringPtrValue(req.DeviceId),
	}

	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	ac.logger.LogInfo(c.Request.Context(), "Processing token refresh", map[string]interface{}{
		"ip":         ipAddress,
		"user_agent": userAgent,
	})

	tokens, err := ac.authUsecase.RefreshToken(c.Request.Context(), domainReq, ipAddress, userAgent)
	if err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Token refresh failed", map[string]interface{}{
			"ip":    ipAddress,
			"error": err.Error(),
		})
		ac.handleError(c, err)
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "Token refreshed successfully", map[string]interface{}{
		"ip": ipAddress,
	})

	apiResponse := api.AuthResponse{
		AccessToken:  &tokens.AccessToken,
		RefreshToken: &tokens.RefreshToken,
	}
	response.Success(c, apiResponse)
}

func (ac *AuthController) GetAuthVerifyEmail(c *gin.Context, params api.GetAuthVerifyEmailParams) {
	domainReq := &domain.EmailVerificationRequest{
		Token: params.Token,
	}

	ac.logger.LogInfo(c.Request.Context(), "Processing email verification", map[string]interface{}{
		"ip": c.ClientIP(),
	})

	authResponse, err := ac.authUsecase.VerifyEmail(c.Request.Context(), domainReq)
	if err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Email verification failed", map[string]interface{}{
			"ip":    c.ClientIP(),
			"error": err.Error(),
		})
		ac.handleError(c, err)
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "Email verified successfully", map[string]interface{}{
		"user_id": authResponse.User.ID,
		"email":   authResponse.User.Email,
	})

	apiResponse := api.AuthResponse{
		User:         ac.convertUserToAPI(authResponse.User),
		AccessToken:  &authResponse.Tokens.AccessToken,
		RefreshToken: &authResponse.Tokens.RefreshToken,
	}

	response.Success(c, apiResponse)
}

func (ac *AuthController) PostAuthResendVerification(c *gin.Context) {
	var req api.ResendVerificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Invalid resend verification request format", map[string]interface{}{
			"error": err.Error(),
			"ip":    c.ClientIP(),
		})
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request format", nil)
		return
	}

	domainReq := &domain.ResendVerificationRequest{
		Email: string(req.Email),
	}

	ac.logger.LogInfo(c.Request.Context(), "Processing resend verification", map[string]interface{}{
		"email": string(req.Email),
		"ip":    c.ClientIP(),
	})

	err := ac.authUsecase.ResendEmailVerification(c.Request.Context(), domainReq)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to resend verification email", map[string]interface{}{
			"email": string(req.Email),
		})
		ac.handleError(c, err)
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "Verification email sent successfully", map[string]interface{}{
		"email": string(req.Email),
	})

	messageResponse := api.MessageResponse{
		Message: stringPtr("Verification email sent successfully"),
		Success: boolPtr(true),
	}
	response.Success(c, messageResponse)
}

func (ac *AuthController) PostAuthForgotPassword(c *gin.Context) {
	var req api.ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Invalid forgot password request format", map[string]interface{}{
			"error": err.Error(),
			"ip":    c.ClientIP(),
		})
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request format", nil)
		return
	}

	domainReq := &domain.ForgotPasswordRequest{
		Email: string(req.Email),
	}

	ac.logger.LogInfo(c.Request.Context(), "Processing forgot password", map[string]interface{}{
		"email": string(req.Email),
		"ip":    c.ClientIP(),
	})

	err := ac.authUsecase.ForgotPassword(c.Request.Context(), domainReq)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to process forgot password", map[string]interface{}{
			"email": string(req.Email),
		})
		ac.handleError(c, err)
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "Password reset email sent successfully", map[string]interface{}{
		"email": string(req.Email),
	})

	messageResponse := api.MessageResponse{
		Message: stringPtr("Password reset email sent successfully"),
		Success: boolPtr(true),
	}
	response.Success(c, messageResponse)
}

func (ac *AuthController) PostAuthResetPassword(c *gin.Context) {
	var req api.ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Invalid reset password request format", map[string]interface{}{
			"error": err.Error(),
			"ip":    c.ClientIP(),
		})
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request format", nil)
		return
	}

	domainReq := &domain.ResetPasswordRequest{
		Token:    req.Token,
		Password: req.Password,
	}

	ac.logger.LogInfo(c.Request.Context(), "Processing password reset", map[string]interface{}{
		"ip": c.ClientIP(),
	})

	err := ac.authUsecase.ResetPassword(c.Request.Context(), domainReq)
	if err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Password reset failed", map[string]interface{}{
			"ip":    c.ClientIP(),
			"error": err.Error(),
		})
		ac.handleError(c, err)
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "Password reset successfully", map[string]interface{}{
		"ip": c.ClientIP(),
	})

	messageResponse := api.MessageResponse{
		Message: stringPtr("Password reset successfully"),
		Success: boolPtr(true),
	}
	response.Success(c, messageResponse)
}

func (ac *AuthController) PostAuthChangePassword(c *gin.Context) {
	var req api.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Invalid change password request format", map[string]interface{}{
			"error": err.Error(),
			"ip":    c.ClientIP(),
		})
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request format", nil)
		return
	}

	userID, err := ac.getUserIDFromContext(c)
	if err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Authentication required for password change", map[string]interface{}{
			"ip": c.ClientIP(),
		})
		response.Unauthorized(c, "UNAUTHORIZED", "Authentication required")
		return
	}

	domainReq := &domain.ChangePasswordRequest{
		CurrentPassword: req.CurrentPassword,
		NewPassword:     req.NewPassword,
	}

	ac.logger.LogInfo(c.Request.Context(), "Processing password change", map[string]interface{}{
		"user_id": userID,
	})

	err = ac.authUsecase.ChangePassword(c.Request.Context(), userID, domainReq)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to change password", map[string]interface{}{
			"user_id": userID,
		})
		ac.handleError(c, err)
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "Password changed successfully", map[string]interface{}{
		"user_id": userID,
	})

	messageResponse := api.MessageResponse{
		Message: stringPtr("Password changed successfully"),
		Success: boolPtr(true),
	}
	response.Success(c, messageResponse)
}

func (ac *AuthController) GetAuthSessions(c *gin.Context) {
	userID, err := ac.getUserIDFromContext(c)
	if err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Authentication required for sessions", map[string]interface{}{
			"ip": c.ClientIP(),
		})
		response.Unauthorized(c, "UNAUTHORIZED", "Authentication required")
		return
	}

	sessionID, err := ac.getSessionIDFromContext(c)
	if err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Invalid session", map[string]interface{}{
			"user_id": userID,
		})
		response.Unauthorized(c, "UNAUTHORIZED", "Invalid session")
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "Fetching user sessions", map[string]interface{}{
		"user_id": userID,
	})

	sessionsResponse, err := ac.authUsecase.GetUserSessions(c.Request.Context(), userID, sessionID)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get user sessions", map[string]interface{}{
			"user_id": userID,
		})
		ac.handleError(c, err)
		return
	}

	apiSessions := make([]api.SessionResponse, 0, len(sessionsResponse.Sessions))
	for _, session := range sessionsResponse.Sessions {
		apiSession := api.SessionResponse{
			Id:         &session.ID,
			DeviceId:   &session.DeviceID,
			DeviceName: &session.DeviceName,
			DeviceType: &session.DeviceType,
			IpAddress:  &session.IPAddress,
			LastUsedAt: &session.LastUsedAt,
			CreatedAt:  &session.CreatedAt,
			IsCurrent:  &session.IsCurrent,
		}
		apiSessions = append(apiSessions, apiSession)
	}

	sessionsList := api.SessionsResponse{
		Sessions: &apiSessions,
	}
	response.Success(c, sessionsList)
}

func (ac *AuthController) DeleteAuthSessionsSessionId(c *gin.Context, sessionId uuid.UUID) {
	userID, err := ac.getUserIDFromContext(c)
	if err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Authentication required for session deletion", map[string]interface{}{
			"ip": c.ClientIP(),
		})
		response.Unauthorized(c, "UNAUTHORIZED", "Authentication required")
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "Terminating user session", map[string]interface{}{
		"user_id":    userID,
		"session_id": sessionId,
	})

	err = ac.authUsecase.LogoutSession(c.Request.Context(), userID, sessionId)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to terminate session", map[string]interface{}{
			"user_id":    userID,
			"session_id": sessionId,
		})
		ac.handleError(c, err)
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "Session terminated successfully", map[string]interface{}{
		"user_id":    userID,
		"session_id": sessionId,
	})

	messageResponse := api.MessageResponse{
		Message: stringPtr("Session terminated successfully"),
		Success: boolPtr(true),
	}
	response.Success(c, messageResponse)
}

func (ac *AuthController) PostAuthSessionsLogoutAll(c *gin.Context) {
	userID, err := ac.getUserIDFromContext(c)
	if err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Authentication required for logout all", map[string]interface{}{
			"ip": c.ClientIP(),
		})
		response.Unauthorized(c, "UNAUTHORIZED", "Authentication required")
		return
	}

	sessionID, err := ac.getSessionIDFromContext(c)
	if err != nil {
		ac.logger.LogWarn(c.Request.Context(), "Invalid session for logout all", map[string]interface{}{
			"user_id": userID,
		})
		response.Unauthorized(c, "UNAUTHORIZED", "Invalid session")
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "Terminating all other user sessions", map[string]interface{}{
		"user_id":    userID,
		"session_id": sessionID,
	})

	err = ac.authUsecase.LogoutAllOtherSessions(c.Request.Context(), userID, sessionID)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to terminate other sessions", map[string]interface{}{
			"user_id":    userID,
			"session_id": sessionID,
		})
		ac.handleError(c, err)
		return
	}

	ac.logger.LogInfo(c.Request.Context(), "All other sessions terminated successfully", map[string]interface{}{
		"user_id":    userID,
		"session_id": sessionID,
	})

	messageResponse := api.MessageResponse{
		Message: stringPtr("All other sessions terminated successfully"),
		Success: boolPtr(true),
	}
	response.Success(c, messageResponse)
}

func (ac *AuthController) handleError(c *gin.Context, err error) {
	var tokenValidationError *domain.TokenValidationError
	if errors.As(err, &tokenValidationError) {
		switch tokenValidationError.Type {
		case "TOKEN_EXPIRED":
			response.BadRequest(c, "TOKEN_EXPIRED", tokenValidationError.Message, nil)
		case "TOKEN_USED":
			response.BadRequest(c, "TOKEN_USED", tokenValidationError.Message, nil)
		case "TOKEN_NOT_FOUND":
			response.BadRequest(c, "TOKEN_INVALID", tokenValidationError.Message, nil)
		case "TOKEN_INVALID":
			response.BadRequest(c, "TOKEN_INVALID", tokenValidationError.Message, nil)
		default:
			response.BadRequest(c, "TOKEN_ERROR", tokenValidationError.Message, nil)
		}
		return
	}

	errMsg := err.Error()
	switch {
	case strings.Contains(errMsg, "already exists"):
		response.Conflict(c, "ALREADY_EXISTS", errMsg, nil)
	case strings.Contains(errMsg, "not found"):
		response.NotFound(c, "NOT_FOUND", errMsg)
	case strings.Contains(errMsg, "invalid") || strings.Contains(errMsg, "validation"):
		response.BadRequest(c, "VALIDATION_ERROR", errMsg, nil)
	case strings.Contains(errMsg, "unauthorized") || strings.Contains(errMsg, "password"):
		response.Unauthorized(c, "UNAUTHORIZED", "Invalid credentials")
	case strings.Contains(errMsg, "email not verified"):
		response.Forbidden(c, "EMAIL_NOT_VERIFIED", "Email verification required")
	case strings.Contains(errMsg, "account is not active"):
		response.Forbidden(c, "ACCOUNT_INACTIVE", "Account is not active")
	default:
		response.InternalServerError(c, "INTERNAL_ERROR", "An internal error occurred")
	}
}

func (ac *AuthController) convertUserToAPI(user *domain.User) *api.User {
	preferredLang := api.UserPreferredLanguage(user.PreferredLanguage)
	role := api.UserRole(user.Role)
	status := api.UserStatus(user.Status)

	apiUser := &api.User{
		Id:                     &user.ID,
		Username:               &user.Username,
		Email:                  (*openapi_types.Email)(&user.Email),
		Name:                   &user.Name,
		Phone:                  user.Phone,
		PreferredLanguage:      &preferredLang,
		Role:                   &role,
		Status:                 &status,
		StripeCustomerId:       user.StripeCustomerID,
		DefaultPaymentMethodId: user.DefaultPaymentMethodID,
		AutoPaymentEnabled:     &user.AutoPaymentEnabled,
		NotifyEmail:            &user.NotifyEmail,
		NotifyPush:             &user.NotifyPush,
		EmailVerified:          &user.EmailVerified,
		LastLoginAt:            user.LastLoginAt,
		CreatedAt:              &user.CreatedAt,
		UpdatedAt:              &user.UpdatedAt,
	}
	return apiUser
}

func (ac *AuthController) getUserIDFromContext(c *gin.Context) (uuid.UUID, error) {
	userIDStr, exists := c.Get("user_id")
	if !exists {
		return uuid.Nil, errors.New("user ID not found in context")
	}

	userID, ok := userIDStr.(uuid.UUID)
	if !ok {
		return uuid.Nil, errors.New("invalid user ID format")
	}

	return userID, nil
}

func (ac *AuthController) getSessionIDFromContext(c *gin.Context) (uuid.UUID, error) {
	sessionIDStr, exists := c.Get("session_id")
	if !exists {
		return uuid.Nil, errors.New("session ID not found in context")
	}

	sessionID, ok := sessionIDStr.(uuid.UUID)
	if !ok {
		return uuid.Nil, errors.New("invalid session ID format")
	}

	return sessionID, nil
}

func getStringPtrValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func stringPtr(s string) *string {
	return &s
}

func boolPtr(b bool) *bool {
	return &b
}
