package controller

import (
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetUserIDFromContext extracts user ID from the Gin context
// This should be used consistently across all controllers
func GetUserIDFromContext(c *gin.Context) (uuid.UUID, error) {
	userIDValue, exists := c.Get("user_id") // Note: middleware sets "user_id", not "userID"
	if !exists {
		return uuid.Nil, errors.New("user ID not found in context")
	}

	userID, ok := userIDValue.(uuid.UUID)
	if !ok {
		return uuid.Nil, errors.New("invalid user ID type in context")
	}

	return userID, nil
}

// GetSessionIDFromContext extracts session ID from the Gin context
func GetSessionIDFromContext(c *gin.Context) (uuid.UUID, error) {
	sessionIDValue, exists := c.Get("session_id")
	if !exists {
		return uuid.Nil, errors.New("session ID not found in context")
	}

	sessionID, ok := sessionIDValue.(uuid.UUID)
	if !ok {
		return uuid.Nil, errors.New("invalid session ID type in context")
	}

	return sessionID, nil
}

// GetUserRoleFromContext extracts user role from the Gin context
func GetUserRoleFromContext(c *gin.Context) (string, error) {
	userRoleValue, exists := c.Get("user_role")
	if !exists {
		return "", errors.New("user role not found in context")
	}

	userRole, ok := userRoleValue.(string)
	if !ok {
		return "", errors.New("invalid user role type in context")
	}

	return userRole, nil
}

// GetRequestID extracts request ID from the Gin context
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}
