package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime/types"
	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type ParkingLotController struct {
	parkingLotUsecase usecase.ParkingLotUsecase
	logger            *logger.Logger
}

func NewParkingLotController(parkingLotUsecase usecase.ParkingLotUsecase, logger *logger.Logger) *ParkingLotController {
	return &ParkingLotController{
		parkingLotUsecase: parkingLotUsecase,
		logger:            logger,
	}
}

func (plc *ParkingLotController) GetParkingLots(c *gin.Context, params api.GetParkingLotsParams) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get parking lots not implemented yet"})
}

func (plc *ParkingLotController) GetParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get parking lot by ID not implemented yet"})
}

func (plc *ParkingLotController) GetParkingLotsLotIdAvailability(c *gin.Context, lotId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get parking lot availability not implemented yet"})
}

func (plc *ParkingLotController) GetAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID, params api.GetAdminParkingLotsLotIdPricingConfigsParams) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get admin parking lot pricing configs not implemented yet"})
}

func (plc *ParkingLotController) PostAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Create admin parking lot pricing config not implemented yet"})
}

func (plc *ParkingLotController) DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Delete admin parking lot pricing config not implemented yet"})
}

func (plc *ParkingLotController) GetAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Get admin parking lot pricing config not implemented yet"})
}

func (plc *ParkingLotController) PutAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Update admin parking lot pricing config not implemented yet"})
}

func (plc *ParkingLotController) PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c *gin.Context, lotId types.UUID, configId types.UUID) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Activate admin parking lot pricing config not implemented yet"})
}

func (plc *ParkingLotController) PostAdminPricingConfigsCalculatePreview(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Calculate admin pricing configs preview not implemented yet"})
}

func (plc *ParkingLotController) PostAdminPricingConfigsValidate(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Validate admin pricing configs not implemented yet"})
}
