package repository

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/model"
)

type userRepository struct {
	db *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

func (r *userRepository) Create(ctx context.Context, user *domain.User) error {
	userModel := &model.UserModel{}
	userModel.FromDomain(user)

	err := r.db.WithContext(ctx).Create(userModel).Error
	if err != nil {
		return err
	}

	*user = *userModel.ToDomain()
	return nil
}

func (r *userRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.User, error) {
	var userModel model.UserModel
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&userModel).Error
	if err != nil {
		return nil, err
	}
	return userModel.ToDomain(), nil
}

func (r *userRepository) GetByEmail(ctx context.Context, email string) (*domain.User, error) {
	var userModel model.UserModel
	err := r.db.WithContext(ctx).Where("email = ? AND deleted_at IS NULL", email).First(&userModel).Error
	if err != nil {
		return nil, err
	}
	return userModel.ToDomain(), nil
}

func (r *userRepository) GetByUsername(ctx context.Context, username string) (*domain.User, error) {
	var userModel model.UserModel
	err := r.db.WithContext(ctx).Where("username = ? AND deleted_at IS NULL", username).First(&userModel).Error
	if err != nil {
		return nil, err
	}
	return userModel.ToDomain(), nil
}

func (r *userRepository) GetByEmailOrUsername(ctx context.Context, identifier string) (*domain.User, error) {
	var userModel model.UserModel
	err := r.db.WithContext(ctx).Where("(email = ? OR username = ?) AND deleted_at IS NULL", identifier, identifier).First(&userModel).Error
	if err != nil {
		return nil, err
	}
	return userModel.ToDomain(), nil
}

func (r *userRepository) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.UserModel{}).Where("email = ? AND deleted_at IS NULL", email).Count(&count).Error
	return count > 0, err
}

func (r *userRepository) ExistsByUsername(ctx context.Context, username string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.UserModel{}).Where("username = ? AND deleted_at IS NULL", username).Count(&count).Error
	return count > 0, err
}

func (r *userRepository) Update(ctx context.Context, user *domain.User) error {
	userModel := &model.UserModel{}
	userModel.FromDomain(user)

	err := r.db.WithContext(ctx).Save(userModel).Error
	if err != nil {
		return err
	}

	*user = *userModel.ToDomain()
	return nil
}

func (r *userRepository) Delete(ctx context.Context, id uuid.UUID) error {
	result := r.db.WithContext(ctx).Model(&model.UserModel{}).
		Where("id = ? AND deleted_at IS NULL", id).
		Update("deleted_at", "NOW()")
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("user not found or already deleted")
	}
	return nil
}

func (r *userRepository) List(ctx context.Context, limit, offset int) ([]*domain.User, error) {
	var userModels []model.UserModel
	err := r.db.WithContext(ctx).Where("deleted_at IS NULL").Limit(limit).Offset(offset).Find(&userModels).Error
	if err != nil {
		return nil, err
	}

	users := make([]*domain.User, len(userModels))
	for i, userModel := range userModels {
		users[i] = userModel.ToDomain()
	}
	return users, nil
}

func (r *userRepository) CreateUserWithVerificationToken(ctx context.Context, user *domain.User, verificationToken *domain.VerificationToken) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		userModel := &model.UserModel{}
		userModel.FromDomain(user)
		if err := tx.Create(userModel).Error; err != nil {
			return err
		}
		*user = *userModel.ToDomain()

		tokenModel := &model.VerificationTokenModel{}
		tokenModel.FromDomain(verificationToken)
		if err := tx.Create(tokenModel).Error; err != nil {
			return err
		}
		*verificationToken = *tokenModel.ToDomain()

		return nil
	})
}

func (r *userRepository) VerifyEmailWithSession(ctx context.Context, userID uuid.UUID, verificationToken *domain.VerificationToken, session *domain.UserSession) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var userModel model.UserModel
		if err := tx.Where("id = ? AND deleted_at IS NULL", userID).First(&userModel).Error; err != nil {
			return err
		}

		user := userModel.ToDomain()
		user.VerifyEmail()
		userModel.FromDomain(user)
		if err := tx.Save(&userModel).Error; err != nil {
			return err
		}

		if err := verificationToken.MarkAsUsed(); err != nil {
			return err
		}
		tokenModel := &model.VerificationTokenModel{}
		tokenModel.FromDomain(verificationToken)
		if err := tx.Save(tokenModel).Error; err != nil {
			return err
		}

		sessionModel := &model.UserSessionModel{}
		sessionModel.FromDomain(session)
		if err := tx.Create(sessionModel).Error; err != nil {
			return err
		}
		*session = *sessionModel.ToDomain()

		return nil
	})
}

func (r *userRepository) ResetPasswordWithCleanup(ctx context.Context, userID uuid.UUID, newPasswordHash string, verificationToken *domain.VerificationToken) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var userModel model.UserModel
		if err := tx.Where("id = ? AND deleted_at IS NULL", userID).First(&userModel).Error; err != nil {
			return err
		}

		user := userModel.ToDomain()
		user.PasswordHash = newPasswordHash
		userModel.FromDomain(user)
		if err := tx.Save(&userModel).Error; err != nil {
			return err
		}

		if err := verificationToken.MarkAsUsed(); err != nil {
			return err
		}
		tokenModel := &model.VerificationTokenModel{}
		tokenModel.FromDomain(verificationToken)
		if err := tx.Save(tokenModel).Error; err != nil {
			return err
		}

		if err := tx.Delete(&model.UserSessionModel{}, "user_id = ?", userID).Error; err != nil {
			return err
		}

		return nil
	})
}
