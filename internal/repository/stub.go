package repository

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
)

// Stub implementations for all repositories



func NewParkingLotRepository(db *gorm.DB) ParkingLotRepository {
	return &parkingLotRepository{db: db}
}

func NewSessionRepository(db *gorm.DB) SessionRepository {
	return &sessionRepository{db: db}
}

func NewPaymentRepository(db *gorm.DB) PaymentRepository {
	return &paymentRepository{db: db}
}

func NewBookingRepository(db *gorm.DB) BookingRepository {
	return &bookingRepository{db: db}
}

func NewNotificationRepository(db *gorm.DB) NotificationRepository {
	return &notificationRepository{db: db}
}

// Basic struct definitions
type parkingLotRepository struct{ db *gorm.DB }
type sessionRepository struct{ db *gorm.DB }
type paymentRepository struct{ db *gorm.DB }
type bookingRepository struct{ db *gorm.DB }
type notificationRepository struct{ db *gorm.DB }

// Implement interfaces with basic CRUD operations

func (r *parkingLotRepository) Create(ctx context.Context, lot *domain.ParkingLot) error { return nil }
func (r *parkingLotRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, error) {
	return nil, nil
}
func (r *parkingLotRepository) List(ctx context.Context, limit, offset int) ([]*domain.ParkingLot, error) {
	return nil, nil
}
func (r *parkingLotRepository) SearchNearby(ctx context.Context, lat, lng float64, radius int, limit, offset int) ([]*domain.ParkingLot, error) {
	return nil, nil
}
func (r *parkingLotRepository) Update(ctx context.Context, lot *domain.ParkingLot) error { return nil }
func (r *parkingLotRepository) Delete(ctx context.Context, id uuid.UUID) error           { return nil }

func (r *sessionRepository) Create(ctx context.Context, session *domain.Session) error { return nil }
func (r *sessionRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.Session, error) {
	return nil, nil
}
func (r *sessionRepository) GetActiveByPlateID(ctx context.Context, plateID uuid.UUID) (*domain.Session, error) {
	return nil, nil
}
func (r *sessionRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Session, error) {
	return nil, nil
}
func (r *sessionRepository) Update(ctx context.Context, session *domain.Session) error { return nil }
func (r *sessionRepository) Delete(ctx context.Context, id uuid.UUID) error            { return nil }

func (r *paymentRepository) Create(ctx context.Context, payment *domain.Payment) error { return nil }
func (r *paymentRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.Payment, error) {
	return nil, nil
}
func (r *paymentRepository) GetBySessionID(ctx context.Context, sessionID uuid.UUID) (*domain.Payment, error) {
	return nil, nil
}
func (r *paymentRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Payment, error) {
	return nil, nil
}
func (r *paymentRepository) Update(ctx context.Context, payment *domain.Payment) error { return nil }
func (r *paymentRepository) Delete(ctx context.Context, id uuid.UUID) error            { return nil }

func (r *bookingRepository) Create(ctx context.Context, booking *domain.Booking) error { return nil }
func (r *bookingRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.Booking, error) {
	return nil, nil
}
func (r *bookingRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Booking, error) {
	return nil, nil
}
func (r *bookingRepository) Update(ctx context.Context, booking *domain.Booking) error { return nil }
func (r *bookingRepository) Delete(ctx context.Context, id uuid.UUID) error            { return nil }
func (r *bookingRepository) CheckConflict(ctx context.Context, parkingLotID uuid.UUID, startTime, endTime string) (bool, error) {
	return false, nil
}

func (r *notificationRepository) Create(ctx context.Context, notification *domain.Notification) error {
	return nil
}
func (r *notificationRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.Notification, error) {
	return nil, nil
}
func (r *notificationRepository) GetByUserID(ctx context.Context, userID uuid.UUID, unreadOnly bool, limit, offset int) ([]*domain.Notification, error) {
	return nil, nil
}
func (r *notificationRepository) Update(ctx context.Context, notification *domain.Notification) error {
	return nil
}
func (r *notificationRepository) Delete(ctx context.Context, id uuid.UUID) error     { return nil }
func (r *notificationRepository) MarkAsRead(ctx context.Context, id uuid.UUID) error { return nil }
